module.exports = {
  /**
   * 获取门店统计数据（简化版本，解决超时问题）
   * @url admin/analytics/store/sys/getStoreStatsSimple 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} store_id 门店ID（可选）
   * @params {String} theme_id 主题ID（可选）
   * @params {String} region 区域名称（可选）
   * @params {String} timeRange 时间范围类型（可选）
   * @params {Array} dateRange 日期范围（可选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------
    let { store_id, theme_id, region, timeRange, dateRange, excludedThemes } = data;
    console.log("getStoreStatsSimple 接收到的数据:", data);

    try {
      // 引入权限检查工具
      const permissionChecker = require('../../../common/sys/checkStorePermission');

      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo, store_id);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }

      let { accessibleStores } = permissionResult;

      // 构建场次查询条件（使用权限检查工具）
      let sessionWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 1000,
          whereJson: { region: region },
          fieldJson: { _id: true }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);
          if (sessionWhereJson.store_id) {
            if (sessionWhereJson.store_id.$in) {
              sessionWhereJson.store_id = db.command.in(
                sessionWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              if (regionStoreIds.includes(sessionWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                sessionWhereJson.store_id = db.command.in([]);
              }
            }
          } else {
            sessionWhereJson.store_id = db.command.in(regionStoreIds);
          }
        } else {
          sessionWhereJson.store_id = db.command.in([]);
        }
      }

      if (theme_id) {
        sessionWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        sessionWhereJson.theme_id = db.command.nin(excludedThemes);
        if (theme_id) {
          sessionWhereJson.theme_id = db.command.and([
            db.command.eq(theme_id),
            db.command.nin(excludedThemes)
          ]);
        }
      }

      // 如果有日期范围，添加日期筛选（使用时间戳）
      if (dateRange && dateRange.length === 2) {
        sessionWhereJson.session_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      console.log('简化版查询条件:', JSON.stringify(sessionWhereJson, null, 2));

      // 1. 只统计基础数据，避免复杂计算
      let totalSessions = await vk.baseDao.count({
        dbName: "escape-sessions",
        whereJson: sessionWhereJson,
      });

      console.log('总场次数查询结果:', totalSessions);

      // 2. 使用数据库聚合查询统计基础数据
      let totalRevenueInCents = 0;
      let totalPlayers = 0;
      let paymentChannels = {
        booking: { amount: 0, count: 0 },
        tuangou: { amount: 0, count: 0 },
        kaitianbao: { amount: 0, count: 0 },
        wechat: { amount: 0, count: 0 },
        cash: { amount: 0, count: 0 },
        douyin: { amount: 0, count: 0 },
        koubei: { amount: 0, count: 0 }
      };

      try {
        // 使用聚合查询计算总和
        let aggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: null,
            totalRevenue: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$booking_amount', 0] },
                { $ifNull: ['$tuangou_amount', 0] },
                { $ifNull: ['$kaitianbao_amount', 0] },
                { $ifNull: ['$wechat_amount', 0] },
                { $ifNull: ['$cash_amount', 0] },
                { $ifNull: ['$douyin_amount', 0] },
                { $ifNull: ['$koubei_amount', 0] }
              ])
            ),
            totalPlayers: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            ),
            totalBookingAmount: db.command.aggregate.sum({ $ifNull: ['$booking_amount', 0] }),
            totalTuangouAmount: db.command.aggregate.sum({ $ifNull: ['$tuangou_amount', 0] }),
            totalKaitianbaoAmount: db.command.aggregate.sum({ $ifNull: ['$kaitianbao_amount', 0] }),
            totalWechatAmount: db.command.aggregate.sum({ $ifNull: ['$wechat_amount', 0] }),
            totalCashAmount: db.command.aggregate.sum({ $ifNull: ['$cash_amount', 0] }),
            totalDouyinAmount: db.command.aggregate.sum({ $ifNull: ['$douyin_amount', 0] }),
            totalKoubeiAmount: db.command.aggregate.sum({ $ifNull: ['$koubei_amount', 0] }),
            totalBookingCount: db.command.aggregate.sum({ $ifNull: ['$booking_count', 0] }),
            totalTuangouCount: db.command.aggregate.sum({ $ifNull: ['$tuangou_count', 0] }),
            totalKaitianbaoCount: db.command.aggregate.sum({ $ifNull: ['$kaitianbao_count', 0] }),
            totalWechatCount: db.command.aggregate.sum({ $ifNull: ['$wechat_count', 0] }),
            totalCashCount: db.command.aggregate.sum({ $ifNull: ['$cash_count', 0] }),
            totalDouyinCount: db.command.aggregate.sum({ $ifNull: ['$douyin_count', 0] }),
            totalKoubeiCount: db.command.aggregate.sum({ $ifNull: ['$koubei_count', 0] })
          })
          .end();

        if (aggregateResult && aggregateResult.data && aggregateResult.data.length > 0) {
          let result = aggregateResult.data[0];
          totalRevenueInCents = result.totalRevenue || 0;
          totalPlayers = result.totalPlayers || 0;

          // 设置支付渠道数据
          paymentChannels.booking.amount = result.totalBookingAmount || 0;
          paymentChannels.booking.count = result.totalBookingCount || 0;
          paymentChannels.tuangou.amount = result.totalTuangouAmount || 0;
          paymentChannels.tuangou.count = result.totalTuangouCount || 0;
          paymentChannels.kaitianbao.amount = result.totalKaitianbaoAmount || 0;
          paymentChannels.kaitianbao.count = result.totalKaitianbaoCount || 0;
          paymentChannels.wechat.amount = result.totalWechatAmount || 0;
          paymentChannels.wechat.count = result.totalWechatCount || 0;
          paymentChannels.cash.amount = result.totalCashAmount || 0;
          paymentChannels.cash.count = result.totalCashCount || 0;
          paymentChannels.douyin.amount = result.totalDouyinAmount || 0;
          paymentChannels.douyin.count = result.totalDouyinCount || 0;
          paymentChannels.koubei.amount = result.totalKoubeiAmount || 0;
          paymentChannels.koubei.count = result.totalKoubeiCount || 0;

          console.log('聚合查询结果:', {
            totalRevenue: totalRevenueInCents,
            totalPlayers: totalPlayers
          });
        }
      } catch (aggregateError) {
        console.error('聚合查询失败，使用默认值:', aggregateError);
        totalRevenueInCents = 0;
        totalPlayers = 0;
      }

      // 转换总收入为元（保留精度）
      let totalRevenue = parseFloat((totalRevenueInCents / 100).toFixed(2));

      // 转换支付渠道金额为元（保留精度）
      Object.keys(paymentChannels).forEach(channel => {
        paymentChannels[channel].amount = parseFloat((paymentChannels[channel].amount / 100).toFixed(2));
      });

      // 3. 计算平均指标
      let avgPerSession = totalSessions > 0 ? {
        avgRevenue: (totalRevenue / totalSessions).toFixed(2),
        avgPlayers: (totalPlayers / totalSessions).toFixed(1),
        avgCustomerPrice: totalPlayers > 0 ? (totalRevenue / totalPlayers).toFixed(2) : 0
      } : {
        avgRevenue: 0,
        avgPlayers: 0,
        avgCustomerPrice: 0
      };

      // 4. 计算开场率统计（使用聚合查询优化）
      let openingRateStats = await calculateOpeningRateOptimized(sessionWhereJson, dateRange, excludedThemes, vk, db, _);

      // 5. 计算NPC主题占比（使用聚合查询优化）
      let npcStats = await calculateNpcStatsOptimized(sessionWhereJson, excludedThemes, vk, db, _);

      console.log('简化版统计完成');

      res.data = {
        totalSessions,
        totalPlayers,
        totalRevenue,
        avgPerSession,
        paymentChannels,
        openingRateStats,
        npcStats
      };
    } catch (err) {
      console.error('简化版统计数据获取失败：', err);
      return { code: -1, msg: "统计数据获取失败：" + err.message };
    }

    return res;
  },
};

// 计算开场率统计 - 聚合查询优化版本
async function calculateOpeningRateOptimized(sessionWhereJson, dateRange, excludedThemes, vk, db, _) {
  try {
    console.log('开始计算开场率（聚合查询优化版）');

    // 计算时间范围内的天数
    let days = 1;
    if (dateRange && dateRange.length === 2) {
      let startDate = new Date(dateRange[0]);
      let endDate = new Date(dateRange[1]);
      days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) || 1;
      if (days === 0) days = 1;
    }

    // 获取门店列表（根据查询条件）
    let storeWhereJson = {};
    if (sessionWhereJson.store_id) {
      if (sessionWhereJson.store_id.$in) {
        storeWhereJson._id = sessionWhereJson.store_id;
      } else {
        storeWhereJson._id = sessionWhereJson.store_id;
      }
    }

    let storeList = await vk.baseDao.selects({
      dbName: "escape-stores",
      whereJson: storeWhereJson,
      pageSize: 1000
    });

    if (!storeList || !storeList.rows || storeList.rows.length === 0) {
      return {
        total_opening_rate: 0,
        total_preset_sessions: 0,
        total_actual_sessions: 0,
        store_opening_rates: []
      };
    }

    // 获取主题列表（排除指定主题）
    let themeWhereJson = {};
    if (storeList.rows.length === 1) {
      themeWhereJson.store_id = storeList.rows[0]._id;
    } else {
      themeWhereJson.store_id = db.command.in(storeList.rows.map(store => store._id));
    }
    themeWhereJson.theme_status = 1;

    if (excludedThemes && excludedThemes.length > 0) {
      themeWhereJson._id = _.nin(excludedThemes);
    }

    let themeList = await vk.baseDao.selects({
      dbName: "escape-themes",
      whereJson: themeWhereJson,
      pageSize: 1000
    });

    // 获取预设场次数据
    let themeSessionWhereJson = { is_active: true };
    if (excludedThemes && excludedThemes.length > 0) {
      themeSessionWhereJson.theme_id = _.nin(excludedThemes);
    }

    let allThemeSessions = await vk.baseDao.selects({
      dbName: "escape-theme-sessions",
      whereJson: themeSessionWhereJson,
      pageSize: 1000
    });

    // 使用聚合查询统计实际场次
    let actualSessionsAggregateResult = await db.collection("escape-sessions")
      .where(sessionWhereJson)
      .aggregate()
      .group({
        _id: {
          store_id: '$store_id',
          theme_id: '$theme_id'
        },
        count: db.command.aggregate.sum(1)
      })
      .end();

    // 创建数据映射
    let themeSessionsMap = {};
    if (allThemeSessions && allThemeSessions.rows) {
      allThemeSessions.rows.forEach(session => {
        if (!themeSessionsMap[session.theme_id]) {
          themeSessionsMap[session.theme_id] = 0;
        }
        themeSessionsMap[session.theme_id]++;
      });
    }

    let actualSessionsMap = {};
    if (actualSessionsAggregateResult && actualSessionsAggregateResult.data) {
      actualSessionsAggregateResult.data.forEach(item => {
        let key = `${item._id.store_id}_${item._id.theme_id}`;
        actualSessionsMap[key] = item.count;
      });
    }

    // 计算开场率
    let totalPresetSessions = 0;
    let totalActualSessions = 0;
    let storeOpeningRates = [];

    for (let store of storeList.rows) {
      let storePresetSessions = 0;
      let storeActualSessions = 0;

      // 获取该门店的主题
      let storeThemes = themeList.rows ? themeList.rows.filter(theme => theme.store_id === store._id) : [];

      for (let theme of storeThemes) {
        // 预设场次数
        let presetCount = themeSessionsMap[theme._id] || 0;
        storePresetSessions += presetCount * days;

        // 实际场次数
        let key = `${store._id}_${theme._id}`;
        let actualCount = actualSessionsMap[key] || 0;
        storeActualSessions += actualCount;
      }

      totalPresetSessions += storePresetSessions;
      totalActualSessions += storeActualSessions;

      // 计算门店开场率
      let openingRate = storePresetSessions > 0 ?
        ((storeActualSessions / storePresetSessions) * 100).toFixed(1) : 0;

      storeOpeningRates.push({
        store_id: store._id,
        store_name: store.store_name,
        preset_sessions: storePresetSessions,
        actual_sessions: storeActualSessions,
        opening_rate: parseFloat(openingRate)
      });
    }

    // 计算总开场率
    let totalOpeningRate = totalPresetSessions > 0 ?
      ((totalActualSessions / totalPresetSessions) * 100).toFixed(1) : 0;

    return {
      total_opening_rate: parseFloat(totalOpeningRate),
      total_preset_sessions: totalPresetSessions,
      total_actual_sessions: totalActualSessions,
      store_opening_rates: storeOpeningRates
    };
  } catch (err) {
    console.error('计算开场率失败：', err);
    return {
      total_opening_rate: 0,
      total_preset_sessions: 0,
      total_actual_sessions: 0,
      store_opening_rates: []
    };
  }
}

// 计算NPC主题占比统计 - 聚合查询优化版本
async function calculateNpcStatsOptimized(sessionWhereJson, excludedThemes, vk, db, _) {
  try {
    console.log('开始计算NPC统计（聚合查询优化版）');

    // 使用聚合查询按主题统计场次和收入
    let sessionAggregateResult = await db.collection("escape-sessions")
      .where(sessionWhereJson)
      .aggregate()
      .group({
        _id: '$theme_id',
        sessionCount: db.command.aggregate.sum(1),
        revenueSum: db.command.aggregate.sum(
          db.command.aggregate.add([
            { $ifNull: ['$booking_amount', 0] },
            { $ifNull: ['$tuangou_amount', 0] },
            { $ifNull: ['$kaitianbao_amount', 0] },
            { $ifNull: ['$wechat_amount', 0] },
            { $ifNull: ['$cash_amount', 0] },
            { $ifNull: ['$douyin_amount', 0] },
            { $ifNull: ['$koubei_amount', 0] }
          ])
        )
      })
      .end();

    if (!sessionAggregateResult || !sessionAggregateResult.data || sessionAggregateResult.data.length === 0) {
      return {
        npc_sessions: 0,
        non_npc_sessions: 0,
        npc_revenue: '0.00',
        non_npc_revenue: '0.00',
        npc_session_rate: 0,
        npc_revenue_rate: 0
      };
    }

    // 获取所有相关主题的NPC信息
    let themeIds = sessionAggregateResult.data.map(item => item._id).filter(id => id);

    if (themeIds.length === 0) {
      return {
        npc_sessions: 0,
        non_npc_sessions: 0,
        npc_revenue: '0.00',
        non_npc_revenue: '0.00',
        npc_session_rate: 0,
        npc_revenue_rate: 0
      };
    }

    let themes = await vk.baseDao.selects({
      dbName: "escape-themes",
      whereJson: {
        _id: _.in(themeIds)
      },
      fieldJson: {
        _id: true,
        has_npc: true
      },
      pageSize: 1000
    });

    // 创建主题NPC映射
    let themeNpcMap = {};
    if (themes && themes.rows) {
      themes.rows.forEach(theme => {
        themeNpcMap[theme._id] = theme.has_npc || false;
      });
    }

    // 统计NPC和非NPC数据
    let npcSessions = 0;
    let nonNpcSessions = 0;
    let npcRevenueInCents = 0;
    let nonNpcRevenueInCents = 0;

    sessionAggregateResult.data.forEach(item => {
      let themeId = item._id;
      let isNpc = themeNpcMap[themeId] || false;
      let sessionCount = item.sessionCount || 0;
      let revenueSum = item.revenueSum || 0;

      if (isNpc) {
        npcSessions += sessionCount;
        npcRevenueInCents += revenueSum;
      } else {
        nonNpcSessions += sessionCount;
        nonNpcRevenueInCents += revenueSum;
      }
    });

    // 处理定金数据（如果需要）
    try {
      // 构建定金查询条件
      let depositWhereJson = {};
      if (sessionWhereJson.store_id) {
        depositWhereJson.store_id = sessionWhereJson.store_id;
      }
      if (sessionWhereJson.session_date) {
        depositWhereJson.deposit_date = sessionWhereJson.session_date;
      }
      if (excludedThemes && excludedThemes.length > 0) {
        depositWhereJson.theme_id = _.nin(excludedThemes);
      }

      // 使用聚合查询统计定金数据
      let depositAggregateResult = await db.collection("escape-deposits")
        .where(depositWhereJson)
        .aggregate()
        .group({
          _id: '$theme_id',
          totalAmount: db.command.aggregate.sum('$deposit_amount')
        })
        .end();

      if (depositAggregateResult && depositAggregateResult.data) {
        depositAggregateResult.data.forEach(item => {
          let themeId = item._id;
          let isNpc = themeNpcMap[themeId] || false;
          let depositAmount = item.totalAmount || 0;

          if (isNpc) {
            npcRevenueInCents += depositAmount;
          } else {
            nonNpcRevenueInCents += depositAmount;
          }
        });
      }
    } catch (depositError) {
      console.error('定金数据统计失败，跳过:', depositError);
    }

    // 转换收入为元
    let npcRevenue = parseFloat((npcRevenueInCents / 100).toFixed(2));
    let nonNpcRevenue = parseFloat((nonNpcRevenueInCents / 100).toFixed(2));
    let totalSessions = npcSessions + nonNpcSessions;
    let totalRevenue = npcRevenue + nonNpcRevenue;

    console.log('NPC统计计算完成:', {
      npc_sessions: npcSessions,
      non_npc_sessions: nonNpcSessions,
      total_sessions: totalSessions
    });

    return {
      npc_sessions: npcSessions,
      non_npc_sessions: nonNpcSessions,
      npc_revenue: npcRevenue.toFixed(2),
      non_npc_revenue: nonNpcRevenue.toFixed(2),
      npc_session_rate: totalSessions > 0 ? ((npcSessions / totalSessions) * 100).toFixed(1) : 0,
      npc_revenue_rate: totalRevenue > 0 ? ((npcRevenue / totalRevenue) * 100).toFixed(1) : 0
    };
  } catch (err) {
    console.error('计算NPC统计失败：', err);
    return {
      npc_sessions: 0,
      non_npc_sessions: 0,
      npc_revenue: '0.00',
      non_npc_revenue: '0.00',
      npc_session_rate: 0,
      npc_revenue_rate: 0
    };
  }
}
