module.exports = {
  /**
   * 获取门店统计数据（简化版本，解决超时问题）
   * @url admin/analytics/store/sys/getStoreStatsSimple 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} store_id 门店ID（可选）
   * @params {String} theme_id 主题ID（可选）
   * @params {String} region 区域名称（可选）
   * @params {String} timeRange 时间范围类型（可选）
   * @params {Array} dateRange 日期范围（可选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------
    let { store_id, theme_id, region, timeRange, dateRange, excludedThemes } = data;
    console.log("getStoreStatsSimple 接收到的数据:", data);

    try {
      // 引入权限检查工具
      const permissionChecker = require('../../../common/sys/checkStorePermission');

      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo, store_id);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }

      let { accessibleStores } = permissionResult;

      // 构建场次查询条件（使用权限检查工具）
      let sessionWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 1000,
          whereJson: { region: region },
          fieldJson: { _id: true }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);
          if (sessionWhereJson.store_id) {
            if (sessionWhereJson.store_id.$in) {
              sessionWhereJson.store_id = db.command.in(
                sessionWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              if (regionStoreIds.includes(sessionWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                sessionWhereJson.store_id = db.command.in([]);
              }
            }
          } else {
            sessionWhereJson.store_id = db.command.in(regionStoreIds);
          }
        } else {
          sessionWhereJson.store_id = db.command.in([]);
        }
      }

      if (theme_id) {
        sessionWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        sessionWhereJson.theme_id = db.command.nin(excludedThemes);
        if (theme_id) {
          sessionWhereJson.theme_id = db.command.and([
            db.command.eq(theme_id),
            db.command.nin(excludedThemes)
          ]);
        }
      }

      // 如果有日期范围，添加日期筛选（使用时间戳）
      if (dateRange && dateRange.length === 2) {
        sessionWhereJson.session_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      console.log('简化版查询条件:', JSON.stringify(sessionWhereJson, null, 2));

      // 1. 只统计基础数据，避免复杂计算
      let totalSessions = await vk.baseDao.count({
        dbName: "escape-sessions",
        whereJson: sessionWhereJson,
      });

      console.log('总场次数查询结果:', totalSessions);

      // 2. 使用数据库聚合查询统计基础数据
      let totalRevenueInCents = 0;
      let totalPlayers = 0;
      let paymentChannels = {
        booking: { amount: 0, count: 0 },
        tuangou: { amount: 0, count: 0 },
        kaitianbao: { amount: 0, count: 0 },
        wechat: { amount: 0, count: 0 },
        cash: { amount: 0, count: 0 },
        douyin: { amount: 0, count: 0 },
        koubei: { amount: 0, count: 0 }
      };

      try {
        // 使用聚合查询计算总和
        let aggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: null,
            totalRevenue: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$booking_amount', 0] },
                { $ifNull: ['$tuangou_amount', 0] },
                { $ifNull: ['$kaitianbao_amount', 0] },
                { $ifNull: ['$wechat_amount', 0] },
                { $ifNull: ['$cash_amount', 0] },
                { $ifNull: ['$douyin_amount', 0] },
                { $ifNull: ['$koubei_amount', 0] }
              ])
            ),
            totalPlayers: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            ),
            totalBookingAmount: db.command.aggregate.sum({ $ifNull: ['$booking_amount', 0] }),
            totalTuangouAmount: db.command.aggregate.sum({ $ifNull: ['$tuangou_amount', 0] }),
            totalKaitianbaoAmount: db.command.aggregate.sum({ $ifNull: ['$kaitianbao_amount', 0] }),
            totalWechatAmount: db.command.aggregate.sum({ $ifNull: ['$wechat_amount', 0] }),
            totalCashAmount: db.command.aggregate.sum({ $ifNull: ['$cash_amount', 0] }),
            totalDouyinAmount: db.command.aggregate.sum({ $ifNull: ['$douyin_amount', 0] }),
            totalKoubeiAmount: db.command.aggregate.sum({ $ifNull: ['$koubei_amount', 0] }),
            totalBookingCount: db.command.aggregate.sum({ $ifNull: ['$booking_count', 0] }),
            totalTuangouCount: db.command.aggregate.sum({ $ifNull: ['$tuangou_count', 0] }),
            totalKaitianbaoCount: db.command.aggregate.sum({ $ifNull: ['$kaitianbao_count', 0] }),
            totalWechatCount: db.command.aggregate.sum({ $ifNull: ['$wechat_count', 0] }),
            totalCashCount: db.command.aggregate.sum({ $ifNull: ['$cash_count', 0] }),
            totalDouyinCount: db.command.aggregate.sum({ $ifNull: ['$douyin_count', 0] }),
            totalKoubeiCount: db.command.aggregate.sum({ $ifNull: ['$koubei_count', 0] })
          })
          .end();

        if (aggregateResult && aggregateResult.data && aggregateResult.data.length > 0) {
          let result = aggregateResult.data[0];
          totalRevenueInCents = result.totalRevenue || 0;
          totalPlayers = result.totalPlayers || 0;

          // 设置支付渠道数据
          paymentChannels.booking.amount = result.totalBookingAmount || 0;
          paymentChannels.booking.count = result.totalBookingCount || 0;
          paymentChannels.tuangou.amount = result.totalTuangouAmount || 0;
          paymentChannels.tuangou.count = result.totalTuangouCount || 0;
          paymentChannels.kaitianbao.amount = result.totalKaitianbaoAmount || 0;
          paymentChannels.kaitianbao.count = result.totalKaitianbaoCount || 0;
          paymentChannels.wechat.amount = result.totalWechatAmount || 0;
          paymentChannels.wechat.count = result.totalWechatCount || 0;
          paymentChannels.cash.amount = result.totalCashAmount || 0;
          paymentChannels.cash.count = result.totalCashCount || 0;
          paymentChannels.douyin.amount = result.totalDouyinAmount || 0;
          paymentChannels.douyin.count = result.totalDouyinCount || 0;
          paymentChannels.koubei.amount = result.totalKoubeiAmount || 0;
          paymentChannels.koubei.count = result.totalKoubeiCount || 0;

          console.log('聚合查询结果:', {
            totalRevenue: totalRevenueInCents,
            totalPlayers: totalPlayers
          });
        }
      } catch (aggregateError) {
        console.error('聚合查询失败，使用默认值:', aggregateError);
        totalRevenueInCents = 0;
        totalPlayers = 0;
      }

      // 转换总收入为元（保留精度）
      let totalRevenue = parseFloat((totalRevenueInCents / 100).toFixed(2));

      // 转换支付渠道金额为元（保留精度）
      Object.keys(paymentChannels).forEach(channel => {
        paymentChannels[channel].amount = parseFloat((paymentChannels[channel].amount / 100).toFixed(2));
      });

      // 3. 计算平均指标
      let avgPerSession = totalSessions > 0 ? {
        avgRevenue: (totalRevenue / totalSessions).toFixed(2),
        avgPlayers: (totalPlayers / totalSessions).toFixed(1),
        avgCustomerPrice: totalPlayers > 0 ? (totalRevenue / totalPlayers).toFixed(2) : 0
      } : {
        avgRevenue: 0,
        avgPlayers: 0,
        avgCustomerPrice: 0
      };

      // 4. 简化的开场率和NPC统计（避免复杂计算）
      let openingRateStats = {
        total_opening_rate: 0,
        total_preset_sessions: 0,
        total_actual_sessions: totalSessions,
        store_opening_rates: []
      };

      let npcStats = {
        npc_sessions: 0,
        non_npc_sessions: totalSessions,
        npc_revenue: '0.00',
        non_npc_revenue: totalRevenue.toFixed(2),
        npc_session_rate: 0,
        npc_revenue_rate: 0
      };

      console.log('简化版统计完成');

      res.data = {
        totalSessions,
        totalPlayers,
        totalRevenue,
        avgPerSession,
        paymentChannels,
        openingRateStats,
        npcStats
      };
    } catch (err) {
      console.error('简化版统计数据获取失败：', err);
      return { code: -1, msg: "统计数据获取失败：" + err.message };
    }

    return res;
  },
};
