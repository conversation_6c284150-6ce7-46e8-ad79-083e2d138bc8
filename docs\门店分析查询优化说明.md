# 门店分析查询优化说明

## 问题描述

在查询上月总数据时，门店分析页面出现云函数超时问题，主要原因：

1. **数据量大**：上月数据包含整个月的所有场次和定金记录
2. **一次性查询**：使用 `pageSize: 10000` 或 `pageSize: -1` 一次性查询所有数据
3. **多表关联**：需要同时查询场次、定金、门店、主题等多个表
4. **复杂计算**：需要计算开场率、NPC统计等复杂指标

## 优化方案：分批查询

### 1. 场次数据分批查询

**优化前：**
```javascript
let allSessions = await vk.baseDao.select({
  dbName: "escape-sessions",
  whereJson: sessionWhereJson,
  pageSize: -1 // 查询所有数据
});
```

**优化后：**
```javascript
let allSessionsData = [];
let currentPage = 1;
let hasMoreData = true;
const batchSize = 1000;

while (hasMoreData) {
  let batchSessions = await vk.baseDao.select({
    dbName: "escape-sessions",
    whereJson: sessionWhereJson,
    pageIndex: currentPage,
    pageSize: batchSize,
    fieldJson: {
      // 只查询需要的字段，减少数据传输
      booking_amount: true,
      tuangou_amount: true,
      // ... 其他必要字段
    }
  });
  
  if (batchSessions && batchSessions.rows && batchSessions.rows.length > 0) {
    allSessionsData = allSessionsData.concat(batchSessions.rows);
    
    if (batchSessions.rows.length < batchSize) {
      hasMoreData = false;
    } else {
      currentPage++;
    }
  } else {
    hasMoreData = false;
  }
  
  // 防止无限循环
  if (currentPage > 100) {
    hasMoreData = false;
  }
}
```

### 2. 定金数据分批查询

同样的优化策略应用到定金数据查询：

```javascript
// 分批查询定金数据，每批1000条
let allDepositsData = [];
currentPage = 1;
hasMoreData = true;

while (hasMoreData) {
  let batchDeposits = await vk.baseDao.select({
    dbName: "escape-deposits",
    whereJson: depositWhereJson,
    pageIndex: currentPage,
    pageSize: batchSize,
    fieldJson: {
      // 只查询需要的字段
      deposit_amount: true,
      payment_method: true,
      payment_channels: true,
      store_id: true,
      theme_id: true,
      deposit_date: true
    }
  });
  
  // ... 处理逻辑
}
```

### 3. 字段选择优化

通过 `fieldJson` 参数只查询必要的字段，减少数据传输量：

- 场次数据：只查询金额、人数、门店ID、主题ID等必要字段
- 定金数据：只查询金额、支付方式、门店ID、主题ID等必要字段

### 4. 循环保护机制

添加最大页数限制（100页），防止无限循环：

```javascript
// 防止无限循环
if (currentPage > 100) {
  console.log('达到最大页数限制，停止查询');
  hasMoreData = false;
}
```

## 优化效果

### 1. 内存使用优化
- **优化前**：一次性加载所有数据到内存
- **优化后**：分批处理，内存使用更稳定

### 2. 查询超时优化
- **优化前**：大数据量查询容易超时
- **优化后**：分批查询，每批数据量可控

### 3. 数据传输优化
- **优化前**：查询所有字段
- **优化后**：只查询必要字段，减少网络传输

### 4. 错误处理优化
- 添加了循环保护机制
- 每批查询都有独立的错误处理

## 涉及文件

1. `uniCloud-alipay/cloudfunctions/router/service/admin/analytics/store/sys/getStoreStats.js`
   - 优化了场次数据查询
   - 优化了定金数据查询
   - 优化了开场率统计计算
   - 优化了NPC统计计算

2. `uniCloud-alipay/cloudfunctions/router/service/admin/analytics/store/sys/getChartData.js`
   - 优化了场次数据查询
   - 优化了定金数据查询
   - 优化了图表数据处理

## 使用建议

1. **批次大小**：当前设置为1000条/批，可根据实际情况调整
2. **字段选择**：根据业务需要调整 `fieldJson` 中的字段
3. **监控日志**：关注控制台输出的分批查询日志
4. **性能测试**：在生产环境中测试优化效果

## 注意事项

1. 分批查询会增加数据库连接次数，但减少单次查询压力
2. 代码复杂度略有增加，但提高了稳定性
3. 建议在数据库层面为常用查询字段添加索引
4. 可以考虑为历史数据实现缓存机制进一步优化
