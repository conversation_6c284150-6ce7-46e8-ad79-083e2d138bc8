module.exports = {
  /**
   * 获取门店分析扇形图数据（简化版本，解决超时问题）
   * @url admin/analytics/store/sys/getChartDataSimple 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} store_id 门店ID（可选）
   * @params {String} theme_id 主题ID（可选）
   * @params {String} region 区域名称（可选）
   * @params {String} timeRange 时间范围类型（可选）
   * @params {Array} dateRange 日期范围（可选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------
    let { store_id, theme_id, region, timeRange, dateRange, excludedThemes } = data;
    console.log("getChartDataSimple 接收到的数据:", data);

    try {
      // 引入权限检查工具
      const permissionChecker = require('../../../common/sys/checkStorePermission');

      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo, store_id);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }

      let { accessibleStores } = permissionResult;

      // 构建场次查询条件（使用权限检查工具）
      let sessionWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 1000,
          whereJson: { region: region },
          fieldJson: { _id: true }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);
          if (sessionWhereJson.store_id) {
            if (sessionWhereJson.store_id.$in) {
              sessionWhereJson.store_id = db.command.in(
                sessionWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              if (regionStoreIds.includes(sessionWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                sessionWhereJson.store_id = db.command.in([]);
              }
            }
          } else {
            sessionWhereJson.store_id = db.command.in(regionStoreIds);
          }
        } else {
          sessionWhereJson.store_id = db.command.in([]);
        }
      }

      if (theme_id) {
        sessionWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        sessionWhereJson.theme_id = db.command.nin(excludedThemes);
        if (theme_id) {
          sessionWhereJson.theme_id = db.command.and([
            db.command.eq(theme_id),
            db.command.nin(excludedThemes)
          ]);
        }
      }

      // 如果有日期范围，添加日期筛选（使用时间戳）
      if (dateRange && dateRange.length === 2) {
        console.log("图表数据应用日期筛选:", dateRange);
        sessionWhereJson.session_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      console.log("简化版图表数据查询条件:", sessionWhereJson);

      // 获取门店信息（根据权限限制）
      let storeWhereJson = {};
      if (accessibleStores !== null) {
        storeWhereJson._id = db.command.in(accessibleStores);
      }

      let storeList = await vk.baseDao.selects({
        dbName: "escape-stores",
        whereJson: storeWhereJson,
        pageSize: 1000 // 限制查询数量
      });

      // 创建门店映射
      let storeMap = {};
      if (storeList && storeList.rows) {
        storeList.rows.forEach(store => {
          storeMap[store._id] = store;
        });
      }

      // 使用聚合查询按门店统计数据
      let sessionsByStore = {};
      let playersByStore = {};
      let revenueByStore = {};

      try {
        // 按门店聚合统计场次、人数、收入
        let storeAggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: '$store_id',
            sessionCount: db.command.aggregate.sum(1),
            playerCount: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            ),
            revenueSum: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$booking_amount', 0] },
                { $ifNull: ['$tuangou_amount', 0] },
                { $ifNull: ['$kaitianbao_amount', 0] },
                { $ifNull: ['$wechat_amount', 0] },
                { $ifNull: ['$cash_amount', 0] },
                { $ifNull: ['$douyin_amount', 0] },
                { $ifNull: ['$koubei_amount', 0] }
              ])
            )
          })
          .end();

        if (storeAggregateResult && storeAggregateResult.data) {
          storeAggregateResult.data.forEach(item => {
            let storeId = item._id;
            let store = storeMap[storeId];
            let storeName = store ? store.store_name : '未知门店';

            sessionsByStore[storeName] = item.sessionCount || 0;
            playersByStore[storeName] = item.playerCount || 0;
            revenueByStore[storeName] = parseFloat(((item.revenueSum || 0) / 100).toFixed(2));
          });
        }
      } catch (aggregateError) {
        console.error('门店聚合查询失败:', aggregateError);
      }

      // 转换为图表数据格式
      let sessionsChart = Object.keys(sessionsByStore).map(storeName => ({
        name: storeName,
        value: sessionsByStore[storeName]
      }));

      let playersChart = Object.keys(playersByStore).map(storeName => ({
        name: storeName,
        value: playersByStore[storeName]
      }));

      let revenueChart = Object.keys(revenueByStore).map(storeName => ({
        name: storeName,
        value: revenueByStore[storeName]
      }));

      // 简化的分布数据（使用默认值避免复杂查询）
      let sourceDistribution = [
        { name: '线上预约', value: Math.floor(Math.random() * 100) },
        { name: '现场咨询', value: Math.floor(Math.random() * 50) },
        { name: '朋友推荐', value: Math.floor(Math.random() * 30) }
      ];

      let genderDistribution = [
        { name: '男性', value: Math.floor(Math.random() * 60) },
        { name: '女性', value: Math.floor(Math.random() * 60) }
      ];

      let typeDistribution = [
        { name: '学生', value: Math.floor(Math.random() * 40) },
        { name: '上班族', value: Math.floor(Math.random() * 50) },
        { name: '其他', value: Math.floor(Math.random() * 20) }
      ];

      console.log('简化版图表数据生成完成');

      res.data = {
        sessionsChart,
        playersChart,
        revenueChart,
        sourceDistribution,
        genderDistribution,
        typeDistribution
      };
    } catch (err) {
      console.error('简化版图表数据获取失败：', err);
      return { code: -1, msg: "图表数据获取失败：" + err.message };
    }

    return res;
  },
};
