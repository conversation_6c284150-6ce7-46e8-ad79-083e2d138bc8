module.exports = {
  /**
   * 获取门店分析扇形图数据（简化版本，解决超时问题）
   * @url admin/analytics/store/sys/getChartDataSimple 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} store_id 门店ID（可选）
   * @params {String} theme_id 主题ID（可选）
   * @params {String} region 区域名称（可选）
   * @params {String} timeRange 时间范围类型（可选）
   * @params {Array} dateRange 日期范围（可选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------
    let { store_id, theme_id, region, timeRange, dateRange, excludedThemes } = data;
    console.log("getChartDataSimple 接收到的数据:", data);

    try {
      // 引入权限检查工具
      const permissionChecker = require('../../../common/sys/checkStorePermission');

      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo, store_id);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }

      let { accessibleStores } = permissionResult;

      // 构建场次查询条件（使用权限检查工具）
      let sessionWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 1000,
          whereJson: { region: region },
          fieldJson: { _id: true }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);
          if (sessionWhereJson.store_id) {
            if (sessionWhereJson.store_id.$in) {
              sessionWhereJson.store_id = db.command.in(
                sessionWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              if (regionStoreIds.includes(sessionWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                sessionWhereJson.store_id = db.command.in([]);
              }
            }
          } else {
            sessionWhereJson.store_id = db.command.in(regionStoreIds);
          }
        } else {
          sessionWhereJson.store_id = db.command.in([]);
        }
      }

      if (theme_id) {
        sessionWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        sessionWhereJson.theme_id = db.command.nin(excludedThemes);
        if (theme_id) {
          sessionWhereJson.theme_id = db.command.and([
            db.command.eq(theme_id),
            db.command.nin(excludedThemes)
          ]);
        }
      }

      // 如果有日期范围，添加日期筛选（使用时间戳）
      if (dateRange && dateRange.length === 2) {
        console.log("图表数据应用日期筛选:", dateRange);
        sessionWhereJson.session_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      console.log("简化版图表数据查询条件:", sessionWhereJson);

      // 获取门店信息（根据权限限制）
      let storeWhereJson = {};
      if (accessibleStores !== null) {
        storeWhereJson._id = db.command.in(accessibleStores);
      }

      let storeList = await vk.baseDao.selects({
        dbName: "escape-stores",
        whereJson: storeWhereJson,
        pageSize: 1000 // 限制查询数量
      });

      // 创建门店映射
      let storeMap = {};
      if (storeList && storeList.rows) {
        storeList.rows.forEach(store => {
          storeMap[store._id] = store;
        });
      }

      // 使用聚合查询按门店统计数据
      let sessionsByStore = {};
      let playersByStore = {};
      let revenueByStore = {};

      try {
        // 按门店聚合统计场次、人数、收入
        let storeAggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: '$store_id',
            sessionCount: db.command.aggregate.sum(1),
            playerCount: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            ),
            revenueSum: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$booking_amount', 0] },
                { $ifNull: ['$tuangou_amount', 0] },
                { $ifNull: ['$kaitianbao_amount', 0] },
                { $ifNull: ['$wechat_amount', 0] },
                { $ifNull: ['$cash_amount', 0] },
                { $ifNull: ['$douyin_amount', 0] },
                { $ifNull: ['$koubei_amount', 0] }
              ])
            )
          })
          .end();

        if (storeAggregateResult && storeAggregateResult.data) {
          storeAggregateResult.data.forEach(item => {
            let storeId = item._id;
            let store = storeMap[storeId];
            let storeName = store ? store.store_name : '未知门店';

            sessionsByStore[storeName] = item.sessionCount || 0;
            playersByStore[storeName] = item.playerCount || 0;
            revenueByStore[storeName] = parseFloat(((item.revenueSum || 0) / 100).toFixed(2));
          });
        }
      } catch (aggregateError) {
        console.error('门店聚合查询失败:', aggregateError);
      }

      // 转换为图表数据格式
      let sessionsChart = Object.keys(sessionsByStore).map(storeName => ({
        name: storeName,
        value: sessionsByStore[storeName]
      }));

      let playersChart = Object.keys(playersByStore).map(storeName => ({
        name: storeName,
        value: playersByStore[storeName]
      }));

      let revenueChart = Object.keys(revenueByStore).map(storeName => ({
        name: storeName,
        value: revenueByStore[storeName]
      }));

      // 使用聚合查询获取真实的分布数据
      let sourceDistribution = [];
      let genderDistribution = [];
      let typeDistribution = [];

      try {
        // 客户来源分布聚合查询
        let sourceAggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: '$customer_source',
            count: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            )
          })
          .end();

        if (sourceAggregateResult && sourceAggregateResult.data) {
          sourceDistribution = sourceAggregateResult.data
            .filter(item => item._id && item.count > 0)
            .map(item => ({
              name: item._id || '未知来源',
              value: item.count
            }));
        }

        // 性别分布聚合查询
        let genderAggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: null,
            maleCount: db.command.aggregate.sum({ $ifNull: ['$male_count', 0] }),
            femaleCount: db.command.aggregate.sum({ $ifNull: ['$female_count', 0] })
          })
          .end();

        if (genderAggregateResult && genderAggregateResult.data && genderAggregateResult.data.length > 0) {
          let result = genderAggregateResult.data[0];
          if (result.maleCount > 0) {
            genderDistribution.push({ name: '男性', value: result.maleCount });
          }
          if (result.femaleCount > 0) {
            genderDistribution.push({ name: '女性', value: result.femaleCount });
          }
        }

        // 客户类型分布聚合查询
        let typeAggregateResult = await db.collection("escape-sessions")
          .where(sessionWhereJson)
          .aggregate()
          .group({
            _id: '$customer_type',
            count: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            )
          })
          .end();

        if (typeAggregateResult && typeAggregateResult.data) {
          typeDistribution = typeAggregateResult.data
            .filter(item => item._id && item.count > 0)
            .map(item => ({
              name: item._id || '未知类型',
              value: item.count
            }));
        }
      } catch (distributionError) {
        console.error('分布数据聚合查询失败:', distributionError);
        // 如果聚合查询失败，使用默认值
        sourceDistribution = [{ name: '数据获取失败', value: 0 }];
        genderDistribution = [{ name: '数据获取失败', value: 0 }];
        typeDistribution = [{ name: '数据获取失败', value: 0 }];
      }

      // 处理定金数据，补充到门店统计中
      try {
        // 构建定金查询条件
        let depositWhereJson = {};
        if (sessionWhereJson.store_id) {
          depositWhereJson.store_id = sessionWhereJson.store_id;
        }
        if (sessionWhereJson.session_date) {
          depositWhereJson.deposit_date = sessionWhereJson.session_date;
        }
        if (excludedThemes && excludedThemes.length > 0) {
          depositWhereJson.theme_id = db.command.nin(excludedThemes);
        }

        // 使用聚合查询按门店统计定金数据
        let depositAggregateResult = await db.collection("escape-deposits")
          .where(depositWhereJson)
          .aggregate()
          .group({
            _id: '$store_id',
            depositRevenue: db.command.aggregate.sum('$deposit_amount'),
            depositCount: db.command.aggregate.sum(1)
          })
          .end();

        if (depositAggregateResult && depositAggregateResult.data) {
          depositAggregateResult.data.forEach(item => {
            let storeId = item._id;
            let store = storeMap[storeId];
            let storeName = store ? store.store_name : '未知门店';

            // 将定金收入添加到门店收入中
            if (revenueByStore[storeName] !== undefined) {
              revenueByStore[storeName] += parseFloat(((item.depositRevenue || 0) / 100).toFixed(2));
            } else {
              revenueByStore[storeName] = parseFloat(((item.depositRevenue || 0) / 100).toFixed(2));
            }
          });

          // 重新生成收入图表数据
          revenueChart = Object.keys(revenueByStore).map(storeName => ({
            name: storeName,
            value: revenueByStore[storeName]
          }));
        }

        // 处理定金数据的客户来源和类型分布
        let depositSourceAggregateResult = await db.collection("escape-deposits")
          .where(depositWhereJson)
          .aggregate()
          .group({
            _id: '$customer_source',
            count: db.command.aggregate.sum(
              db.command.aggregate.add([
                { $ifNull: ['$male_count', 0] },
                { $ifNull: ['$female_count', 0] }
              ])
            )
          })
          .end();

        if (depositSourceAggregateResult && depositSourceAggregateResult.data) {
          depositSourceAggregateResult.data.forEach(item => {
            if (item._id && item.count > 0) {
              let existingSource = sourceDistribution.find(source => source.name === item._id);
              if (existingSource) {
                existingSource.value += item.count;
              } else {
                sourceDistribution.push({
                  name: item._id,
                  value: item.count
                });
              }
            }
          });
        }

        // 处理定金数据的性别分布
        let depositGenderAggregateResult = await db.collection("escape-deposits")
          .where(depositWhereJson)
          .aggregate()
          .group({
            _id: null,
            maleCount: db.command.aggregate.sum({ $ifNull: ['$male_count', 0] }),
            femaleCount: db.command.aggregate.sum({ $ifNull: ['$female_count', 0] })
          })
          .end();

        if (depositGenderAggregateResult && depositGenderAggregateResult.data && depositGenderAggregateResult.data.length > 0) {
          let result = depositGenderAggregateResult.data[0];

          let maleDistribution = genderDistribution.find(item => item.name === '男性');
          if (maleDistribution) {
            maleDistribution.value += result.maleCount || 0;
          } else if (result.maleCount > 0) {
            genderDistribution.push({ name: '男性', value: result.maleCount });
          }

          let femaleDistribution = genderDistribution.find(item => item.name === '女性');
          if (femaleDistribution) {
            femaleDistribution.value += result.femaleCount || 0;
          } else if (result.femaleCount > 0) {
            genderDistribution.push({ name: '女性', value: result.femaleCount });
          }
        }

      } catch (depositError) {
        console.error('定金数据处理失败:', depositError);
      }

      console.log('聚合查询优化版图表数据生成完成');

      res.data = {
        sessionsChart,
        playersChart,
        revenueChart,
        sourceDistribution,
        genderDistribution,
        typeDistribution
      };
    } catch (err) {
      console.error('简化版图表数据获取失败：', err);
      return { code: -1, msg: "图表数据获取失败：" + err.message };
    }

    return res;
  },
};
