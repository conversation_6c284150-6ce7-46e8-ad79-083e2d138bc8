module.exports = {
  /**
   * 获取门店统计数据
   * @url admin/analytics/store/sys/getStoreStats 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} store_id 门店ID（可选）
   * @params {String} theme_id 主题ID（可选）
   * @params {String} region 区域名称（可选）
   * @params {String} timeRange 时间范围类型（可选）
   * @params {Array} dateRange 日期范围（可选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------
    let { store_id, theme_id, region, timeRange, dateRange, excludedThemes } = data;
    console.log("data", data);
    try {
      // 引入权限检查工具
      const permissionChecker = require('../../../common/sys/checkStorePermission');

      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo, store_id);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }

      let { accessibleStores } = permissionResult;

      // 构建场次查询条件（使用权限检查工具）
      let sessionWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 10000,
          whereJson: {
            region: region
          },
          fieldJson: {
            _id: true
          }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);

          // 如果已经有门店ID限制，取交集
          if (sessionWhereJson.store_id) {
            if (sessionWhereJson.store_id.$in) {
              // 如果是数组形式，取交集
              sessionWhereJson.store_id = _.in(
                sessionWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              // 如果是单个门店ID，检查是否在区域内
              if (regionStoreIds.includes(sessionWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                // 门店不在指定区域内，设置为空结果
                sessionWhereJson.store_id = _.in([]);
              }
            }
          } else {
            // 没有门店ID限制，直接使用区域门店
            sessionWhereJson.store_id = _.in(regionStoreIds);
          }
        } else {
          // 该区域下没有门店，设置为空结果
          sessionWhereJson.store_id = _.in([]);
        }
      }

      if (theme_id) {
        sessionWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        console.log('排除主题列表:', excludedThemes);
        sessionWhereJson.theme_id = _.nin(excludedThemes);
        // 如果同时指定了theme_id和excludedThemes，需要特殊处理
        if (theme_id) {
          sessionWhereJson.theme_id = _.and([
            _.eq(theme_id),
            _.nin(excludedThemes)
          ]);
        }
        console.log('应用排除主题后的theme_id条件:', sessionWhereJson.theme_id);
      }

      // 如果有日期范围，添加日期筛选（使用时间戳）
      if (dateRange && dateRange.length === 2) {
        sessionWhereJson.session_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      console.log('最终查询条件:', JSON.stringify(sessionWhereJson, null, 2));

      // 1. 统计场次总数
      let totalSessions = await vk.baseDao.count({
        dbName: "escape-sessions",
        whereJson: sessionWhereJson,
      });

      console.log('总场次数查询结果:', totalSessions);

      // 2. 统计总营业额和预定人数（分批查询优化）
      let totalRevenueInCents = 0; // 以分为单位计算，避免浮点数精度问题
      let totalPlayers = 0;
      let allSessionsData = [];

      // 分批查询场次数据，每批1000条
      const batchSize = 1000;
      let currentPage = 1;
      let hasMoreData = true;

      console.log('开始分批查询场次数据...');

      while (hasMoreData) {
        let batchSessions = await vk.baseDao.select({
          dbName: "escape-sessions",
          whereJson: sessionWhereJson,
          pageIndex: currentPage,
          pageSize: batchSize,
          fieldJson: {
            booking_amount: true,
            tuangou_amount: true,
            kaitianbao_amount: true,
            wechat_amount: true,
            cash_amount: true,
            douyin_amount: true,
            koubei_amount: true,
            booking_count: true,
            tuangou_count: true,
            kaitianbao_count: true,
            wechat_count: true,
            cash_count: true,
            douyin_count: true,
            koubei_count: true,
            male_count: true,
            female_count: true,
            store_id: true,
            theme_id: true,
            session_date: true
          }
        });

        if (batchSessions && batchSessions.rows && batchSessions.rows.length > 0) {
          console.log(`第${currentPage}批查询到${batchSessions.rows.length}条场次数据`);

          // 处理当前批次数据
          batchSessions.rows.forEach((session) => {
            // 计算总营业额（各渠道金额之和，以分为单位）
            let sessionRevenue =
              (session.booking_amount || 0) +
              (session.tuangou_amount || 0) +
              (session.kaitianbao_amount || 0) +
              (session.wechat_amount || 0) +
              (session.cash_amount || 0) +
              (session.douyin_amount || 0) +
              (session.koubei_amount || 0);
            totalRevenueInCents += sessionRevenue; // 保持分为单位

            // 计算预定人数（男女人数之和）
            totalPlayers +=
              (session.male_count || 0) + (session.female_count || 0);
          });

          // 保存数据用于后续计算
          allSessionsData = allSessionsData.concat(batchSessions.rows);

          // 检查是否还有更多数据
          if (batchSessions.rows.length < batchSize) {
            hasMoreData = false;
          } else {
            currentPage++;
          }
        } else {
          hasMoreData = false;
        }

        // 防止无限循环
        if (currentPage > 100) {
          console.log('达到最大页数限制，停止查询');
          hasMoreData = false;
        }
      }

      console.log(`分批查询完成，共获取${allSessionsData.length}条场次数据`);

      // 3. 平均指标计算将在定金统计后进行

      // 4. 统计收款渠道
      let paymentChannels = {
        booking: { amount: 0, count: 0 },
        tuangou: { amount: 0, count: 0 },
        kaitianbao: { amount: 0, count: 0 },
        wechat: { amount: 0, count: 0 },
        cash: { amount: 0, count: 0 },
        douyin: { amount: 0, count: 0 },
        koubei: { amount: 0, count: 0 }
      };

      if (allSessionsData && allSessionsData.length > 0) {
        allSessionsData.forEach((session) => {
          // 统计各渠道金额和人数（包含负数金额）
          if (session.booking_amount !== 0 && session.booking_amount !== null && session.booking_amount !== undefined) {
            paymentChannels.booking.amount += session.booking_amount; // 保持分为单位
            paymentChannels.booking.count += session.booking_count || 0;
          }
          if (session.tuangou_amount !== 0 && session.tuangou_amount !== null && session.tuangou_amount !== undefined) {
            paymentChannels.tuangou.amount += session.tuangou_amount; // 保持分为单位
            paymentChannels.tuangou.count += session.tuangou_count || 0;
          }
          if (session.kaitianbao_amount !== 0 && session.kaitianbao_amount !== null && session.kaitianbao_amount !== undefined) {
            paymentChannels.kaitianbao.amount += session.kaitianbao_amount; // 保持分为单位
            paymentChannels.kaitianbao.count += session.kaitianbao_count || 0;
          }
          if (session.wechat_amount !== 0 && session.wechat_amount !== null && session.wechat_amount !== undefined) {
            paymentChannels.wechat.amount += session.wechat_amount; // 保持分为单位
            paymentChannels.wechat.count += session.wechat_count || 0;
          }
          if (session.cash_amount !== 0 && session.cash_amount !== null && session.cash_amount !== undefined) {
            paymentChannels.cash.amount += session.cash_amount; // 保持分为单位
            paymentChannels.cash.count += session.cash_count || 0;
          }
          if (session.douyin_amount !== 0 && session.douyin_amount !== null && session.douyin_amount !== undefined) {
            paymentChannels.douyin.amount += session.douyin_amount; // 保持分为单位
            paymentChannels.douyin.count += session.douyin_count || 0;
          }
          if (session.koubei_amount !== 0 && session.koubei_amount !== null && session.koubei_amount !== undefined) {
            paymentChannels.koubei.amount += session.koubei_amount; // 保持分为单位
            paymentChannels.koubei.count += session.koubei_count || 0;
          }
        });
      }

      // 4.5. 获取定金记录数据并统计
      let depositWhereJson = {};

      // 构建定金记录查询条件（使用权限检查工具）
      depositWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店（复用之前的逻辑）
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 10000,
          whereJson: {
            region: region
          },
          fieldJson: {
            _id: true
          }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);

          // 如果已经有门店ID限制，取交集
          if (depositWhereJson.store_id) {
            if (depositWhereJson.store_id.$in) {
              // 如果是数组形式，取交集
              depositWhereJson.store_id = _.in(
                depositWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              // 如果是单个门店ID，检查是否在区域内
              if (regionStoreIds.includes(depositWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                // 门店不在指定区域内，设置为空结果
                depositWhereJson.store_id = _.in([]);
              }
            }
          } else {
            // 没有门店ID限制，直接使用区域门店
            depositWhereJson.store_id = _.in(regionStoreIds);
          }
        } else {
          // 该区域下没有门店，设置为空结果
          depositWhereJson.store_id = _.in([]);
        }
      }

      if (theme_id) {
        depositWhereJson.theme_id = theme_id;
      }

      // 如果有日期范围，添加日期筛选（使用收定金日期）
      if (dateRange && dateRange.length === 2) {
        depositWhereJson.deposit_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      // 分批查询定金数据，每批1000条
      let allDepositsData = [];
      currentPage = 1;
      hasMoreData = true;

      console.log('开始分批查询定金数据...');

      while (hasMoreData) {
        let batchDeposits = await vk.baseDao.select({
          dbName: "escape-deposits",
          whereJson: depositWhereJson,
          pageIndex: currentPage,
          pageSize: batchSize,
          fieldJson: {
            deposit_amount: true,
            payment_method: true,
            payment_channels: true,
            store_id: true,
            theme_id: true,
            deposit_date: true
          }
        });

        if (batchDeposits && batchDeposits.rows && batchDeposits.rows.length > 0) {
          console.log(`第${currentPage}批查询到${batchDeposits.rows.length}条定金数据`);
          allDepositsData = allDepositsData.concat(batchDeposits.rows);

          // 检查是否还有更多数据
          if (batchDeposits.rows.length < batchSize) {
            hasMoreData = false;
          } else {
            currentPage++;
          }
        } else {
          hasMoreData = false;
        }

        // 防止无限循环
        if (currentPage > 100) {
          console.log('达到最大页数限制，停止查询');
          hasMoreData = false;
        }
      }

      console.log(`分批查询完成，共获取${allDepositsData.length}条定金数据`);

      // 统计定金记录到对应渠道和总收入
      if (allDepositsData && allDepositsData.length > 0) {
        allDepositsData.forEach((deposit) => {
          let depositAmount = 0;

          // 检查多渠道支付信息
          if (deposit.payment_channels && Array.isArray(deposit.payment_channels)) {
            // 多渠道支付：累加所有渠道的金额
            deposit.payment_channels.forEach(paymentChannel => {
              depositAmount += paymentChannel.amount || 0;
            });
          } else {
            // 单一渠道支付：使用原有逻辑
            depositAmount = deposit.deposit_amount || 0;
          }

          totalRevenueInCents += depositAmount;

          // 统计收款渠道（支持多渠道）
          if (deposit.payment_channels && Array.isArray(deposit.payment_channels)) {
            // 多渠道支付：分别统计每个渠道
            deposit.payment_channels.forEach(paymentChannel => {
              const paymentMethod = paymentChannel.payment_method;
              const channelAmount = paymentChannel.amount || 0;

              if (channelAmount !== 0 && channelAmount !== null && channelAmount !== undefined) {
                if (paymentMethod === '预约') {
                  paymentChannels.booking.amount += channelAmount;
                } else if (paymentMethod === '团购') {
                  paymentChannels.tuangou.amount += channelAmount;
                } else if (paymentMethod === '收款码') {
                  paymentChannels.kaitianbao.amount += channelAmount;
                } else if (paymentMethod === '微信') {
                  paymentChannels.wechat.amount += channelAmount;
                } else if (paymentMethod === '现金') {
                  paymentChannels.cash.amount += channelAmount;
                } else if (paymentMethod === '抖音') {
                  paymentChannels.douyin.amount += channelAmount;
                } else if (paymentMethod === '支付宝') {
                  paymentChannels.koubei.amount += channelAmount;
                }
              }
            });
          } else {
            // 单一渠道支付：使用原有逻辑
            let paymentMethod = deposit.payment_method;
            if (paymentMethod === '预约' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.booking.amount += depositAmount;
            } else if (paymentMethod === '团购' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.tuangou.amount += depositAmount;
            } else if (paymentMethod === '收款码' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.kaitianbao.amount += depositAmount;
            } else if (paymentMethod === '微信' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.wechat.amount += depositAmount;
            } else if (paymentMethod === '现金' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.cash.amount += depositAmount;
            } else if (paymentMethod === '抖音' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.douyin.amount += depositAmount;
            } else if (paymentMethod === '支付宝' && deposit.deposit_amount !== 0 && deposit.deposit_amount !== null && deposit.deposit_amount !== undefined) {
              paymentChannels.koubei.amount += depositAmount;
            }
          }
        });
      }

      // 转换总收入为元（保留精度）
      let totalRevenue = parseFloat((totalRevenueInCents / 100).toFixed(2));

      // 转换支付渠道金额为元（保留精度）
      Object.keys(paymentChannels).forEach(channel => {
        paymentChannels[channel].amount = parseFloat((paymentChannels[channel].amount / 100).toFixed(2));
      });

      // 3. 计算平均指标（包含定金后的总收入）
      let avgPerSession = totalSessions > 0 ? {
        avgRevenue: (totalRevenue / totalSessions).toFixed(2),
        avgPlayers: (totalPlayers / totalSessions).toFixed(1),
        avgCustomerPrice: totalPlayers > 0 ? (totalRevenue / totalPlayers).toFixed(2) : 0
      } : {
        avgRevenue: 0,
        avgPlayers: 0,
        avgCustomerPrice: 0
      };

      // 5. 计算开场率统计
      let openingRateStats = await calculateOpeningRate(sessionWhereJson, dateRange, excludedThemes, vk, _);

      // 6. 计算NPC主题占比
      let npcStats = await calculateNpcStats(sessionWhereJson, excludedThemes, vk, _);

      console.log('npcStats',npcStats);
      console.log('openingRateStats',openingRateStats);
      
      res.data = {
        totalSessions,
        totalPlayers,
        totalRevenue,
        avgPerSession,
        paymentChannels,
        openingRateStats,
        npcStats
      };
    } catch (err) {
      return { code: -1, msg: "统计数据获取失败：" + err.message };
    }

    return res;
  },
};

// 计算开场率统计 - 优化版本
async function calculateOpeningRate(sessionWhereJson, dateRange, excludedThemes, vk, _) {
  try {
    console.log('开始计算开场率，查询条件:', sessionWhereJson);

    // 计算时间范围内的天数
    let days = 1; // 默认为1天
    if (dateRange && dateRange.length === 2) {
      let startDate = new Date(dateRange[0]);
      let endDate = new Date(dateRange[1]);
      // 计算天数：将时间差转换为天数，向上取整
      days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) || 1;
      // 如果是同一天，确保至少是1天
      if (days === 0) days = 1;
    }

    // 获取所有相关的主题和门店
    let storeWhereJson = {};
    let themeWhereJson = {};

    if (sessionWhereJson.store_id) {
      storeWhereJson._id = sessionWhereJson.store_id;
      themeWhereJson.store_id = sessionWhereJson.store_id;
    }
    if (sessionWhereJson.theme_id) {
      themeWhereJson._id = sessionWhereJson.theme_id;
    }

    // 如果有排除主题列表，添加排除条件
    if (excludedThemes && excludedThemes.length > 0) {
      if (themeWhereJson._id) {
        // 如果已经有theme_id条件，需要同时满足
        themeWhereJson._id = _.and([
          themeWhereJson._id,
          _.nin(excludedThemes)
        ]);
      } else {
        themeWhereJson._id = _.nin(excludedThemes);
      }
    }

    // 一次性获取所有需要的数据
    console.log('获取门店列表...');
    let storeList = await vk.baseDao.selects({
      dbName: "escape-stores",
      whereJson: storeWhereJson,
      pageSize: 10000
    });

    console.log('获取主题列表...');
    let themeList = await vk.baseDao.selects({
      dbName: "escape-themes",
      whereJson: themeWhereJson,
      pageSize: 10000
    });

    // 一次性获取所有预设场次数据（排除指定主题）
    console.log('获取所有预设场次...');
    let themeSessionWhereJson = {
      is_active: true
    };

    // 如果有排除主题列表，添加排除条件
    if (excludedThemes && excludedThemes.length > 0) {
      themeSessionWhereJson.theme_id = _.nin(excludedThemes);
    }

    let allThemeSessions = await vk.baseDao.selects({
      dbName: "escape-theme-sessions",
      whereJson: themeSessionWhereJson,
      pageSize: 10000
    });

    // 分批获取所有实际场次数据
    console.log('分批获取所有实际场次...');
    let allActualSessionsData = [];
    let currentPage = 1;
    let hasMoreData = true;
    const batchSize = 1000;

    while (hasMoreData) {
      let batchSessions = await vk.baseDao.select({
        dbName: "escape-sessions",
        whereJson: sessionWhereJson,
        pageIndex: currentPage,
        pageSize: batchSize,
        fieldJson: {
          store_id: true,
          theme_id: true,
          session_date: true
        }
      });

      if (batchSessions && batchSessions.rows && batchSessions.rows.length > 0) {
        allActualSessionsData = allActualSessionsData.concat(batchSessions.rows);

        if (batchSessions.rows.length < batchSize) {
          hasMoreData = false;
        } else {
          currentPage++;
        }
      } else {
        hasMoreData = false;
      }

      // 防止无限循环
      if (currentPage > 100) {
        hasMoreData = false;
      }
    }

    console.log(`分批查询完成，共获取${allActualSessionsData.length}条实际场次数据`);

    // 创建数据映射以提高查询效率
    let themeSessionsMap = {};
    if (allThemeSessions && allThemeSessions.rows) {
      allThemeSessions.rows.forEach(session => {
        if (!themeSessionsMap[session.theme_id]) {
          themeSessionsMap[session.theme_id] = 0;
        }
        themeSessionsMap[session.theme_id]++;
      });
    }

    let actualSessionsMap = {};
    if (allActualSessionsData && allActualSessionsData.length > 0) {
      allActualSessionsData.forEach(session => {
        let key = `${session.store_id}_${session.theme_id}`;
        if (!actualSessionsMap[key]) {
          actualSessionsMap[key] = 0;
        }
        actualSessionsMap[key]++;
      });
    }

    let totalPresetSessions = 0;
    let totalActualSessions = 0;
    let storeOpeningRates = [];

    if (storeList && storeList.rows && themeList && themeList.rows) {
      console.log(`处理 ${storeList.rows.length} 个门店的开场率计算...`);

      for (let store of storeList.rows) {
        let storePresetSessions = 0;
        let storeActualSessions = 0;

        // 获取该门店的主题（已排除指定主题）
        let storeThemes = themeList.rows.filter(theme => theme.store_id === store._id);

        for (let theme of storeThemes) {
          // 从映射中获取预设场次数
          let presetCount = themeSessionsMap[theme._id] || 0;
          // 计算总预设场次（预设场次 × 天数）
          storePresetSessions += presetCount * days;

          // 从映射中获取实际场次数
          let key = `${store._id}_${theme._id}`;
          let actualCount = actualSessionsMap[key] || 0;
          storeActualSessions += actualCount;
        }

        totalPresetSessions += storePresetSessions;
        totalActualSessions += storeActualSessions;

        // 计算门店开场率
        let openingRate = storePresetSessions > 0 ?
          ((storeActualSessions / storePresetSessions) * 100).toFixed(1) : 0;

        storeOpeningRates.push({
          store_name: store.store_name,
          preset_sessions: storePresetSessions,
          actual_sessions: storeActualSessions,
          opening_rate: parseFloat(openingRate)
        });
      }
    }

    // 计算总开场率
    let totalOpeningRate = totalPresetSessions > 0 ?
      ((totalActualSessions / totalPresetSessions) * 100).toFixed(1) : 0;

    console.log('开场率计算完成:', {
      total_opening_rate: parseFloat(totalOpeningRate),
      total_preset_sessions: totalPresetSessions,
      total_actual_sessions: totalActualSessions
    });

    return {
      total_opening_rate: parseFloat(totalOpeningRate),
      total_preset_sessions: totalPresetSessions,
      total_actual_sessions: totalActualSessions,
      store_opening_rates: storeOpeningRates
    };
  } catch (err) {
    console.error('计算开场率失败：', err);
    return {
      total_opening_rate: 0,
      total_preset_sessions: 0,
      total_actual_sessions: 0,
      store_opening_rates: []
    };
  }
}

// 计算NPC主题占比统计 - 优化版本
async function calculateNpcStats(sessionWhereJson, excludedThemes, vk, _) {
  try {
    console.log('开始计算NPC统计，查询条件:', sessionWhereJson);

    // 分批获取所有场次数据
    let allSessionsData = [];
    let currentPage = 1;
    let hasMoreData = true;
    const batchSize = 1000;

    while (hasMoreData) {
      let batchSessions = await vk.baseDao.select({
        dbName: "escape-sessions",
        whereJson: sessionWhereJson,
        pageIndex: currentPage,
        pageSize: batchSize,
        fieldJson: {
          theme_id: true,
          booking_amount: true,
          tuangou_amount: true,
          kaitianbao_amount: true,
          wechat_amount: true,
          cash_amount: true,
          douyin_amount: true,
          koubei_amount: true
        }
      });

      if (batchSessions && batchSessions.rows && batchSessions.rows.length > 0) {
        allSessionsData = allSessionsData.concat(batchSessions.rows);

        if (batchSessions.rows.length < batchSize) {
          hasMoreData = false;
        } else {
          currentPage++;
        }
      } else {
        hasMoreData = false;
      }

      // 防止无限循环
      if (currentPage > 100) {
        hasMoreData = false;
      }
    }

    console.log(`NPC统计分批查询完成，共获取${allSessionsData.length}条场次数据`);

    let npcSessions = 0;
    let nonNpcSessions = 0;
    let npcRevenueInCents = 0; // 以分为单位计算，避免浮点数精度问题
    let nonNpcRevenueInCents = 0; // 以分为单位计算，避免浮点数精度问题

    if (allSessionsData && allSessionsData.length > 0) {
      console.log(`处理 ${allSessionsData.length} 个场次的NPC统计...`);

      // 获取所有相关主题的NPC信息
      let themeIds = [...new Set(allSessionsData.map(session => session.theme_id))];
      console.log('需要查询的主题数量:', themeIds.length);

      // 修复：使用VK框架的正确语法查询主题
      let themes = await vk.baseDao.selects({
        dbName: "escape-themes",
        whereJson: {},
        pageSize: 10000
      });

      // 创建主题NPC映射
      let themeNpcMap = {};
      if (themes && themes.rows) {
        themes.rows.forEach(theme => {
          themeNpcMap[theme._id] = theme.has_npc || false;
        });
      }

      // 统计NPC和非NPC场次及营业额
      allSessionsData.forEach(session => {
        let isNpc = themeNpcMap[session.theme_id] || false;

        // 计算场次营业额（保持分为单位）
        let sessionRevenue = (session.booking_amount || 0) +
                           (session.tuangou_amount || 0) +
                           (session.kaitianbao_amount || 0) +
                           (session.wechat_amount || 0) +
                           (session.cash_amount || 0) +
                           (session.douyin_amount || 0) +
                           (session.koubei_amount || 0);

        if (isNpc) {
          npcSessions++;
          npcRevenueInCents += sessionRevenue;
        } else {
          nonNpcSessions++;
          nonNpcRevenueInCents += sessionRevenue;
        }
      });
    }

    // 获取定金记录并按主题分配到NPC/非NPC
    let depositWhereJson = {};

    // 复制场次查询条件中的权限和日期筛选到定金查询
    if (sessionWhereJson.store_id) {
      depositWhereJson.store_id = sessionWhereJson.store_id;
    }

    // 如果有日期范围，添加日期筛选（使用收定金日期）
    if (sessionWhereJson.session_date) {
      depositWhereJson.deposit_date = sessionWhereJson.session_date;
    }

    // 如果有排除主题列表，添加排除条件
    if (excludedThemes && excludedThemes.length > 0) {
      depositWhereJson.theme_id = _.nin(excludedThemes);
    }

    console.log('开始查询定金数据，查询条件:', depositWhereJson);

    // 分批查询定金数据
    let allDepositsData = [];
    currentPage = 1;
    hasMoreData = true;

    while (hasMoreData) {
      let batchDeposits = await vk.baseDao.select({
        dbName: "escape-deposits",
        whereJson: depositWhereJson,
        pageIndex: currentPage,
        pageSize: batchSize,
        fieldJson: {
          theme_id: true,
          deposit_amount: true,
          payment_method: true,
          payment_channels: true
        }
      });

      if (batchDeposits && batchDeposits.rows && batchDeposits.rows.length > 0) {
        allDepositsData = allDepositsData.concat(batchDeposits.rows);

        if (batchDeposits.rows.length < batchSize) {
          hasMoreData = false;
        } else {
          currentPage++;
        }
      } else {
        hasMoreData = false;
      }

      // 防止无限循环
      if (currentPage > 100) {
        hasMoreData = false;
      }
    }

    console.log(`NPC统计定金分批查询完成，共获取${allDepositsData.length}条定金数据`);

    // 统计定金收入到NPC/非NPC
    if (allDepositsData && allDepositsData.length > 0) {
      // 获取所有定金相关主题的NPC信息（可能与场次主题不同）
      let depositThemeIds = [...new Set(allDepositsData.map(deposit => deposit.theme_id).filter(id => id))];

      if (depositThemeIds.length > 0) {
        console.log('需要查询的定金主题数量:', depositThemeIds.length);

        // 获取定金相关主题的NPC信息
        let depositThemes = await vk.baseDao.selects({
          dbName: "escape-themes",
          whereJson: {},
          pageSize: 10000
        });

        // 创建定金主题NPC映射
        let depositThemeNpcMap = {};
        if (depositThemes && depositThemes.rows) {
          depositThemes.rows.forEach(theme => {
            depositThemeNpcMap[theme._id] = theme.has_npc || false;
          });
        }

        // 统计定金收入到NPC/非NPC
        allDepositsData.forEach(deposit => {
          if (deposit.theme_id) {
            let isNpc = depositThemeNpcMap[deposit.theme_id] || false;
            let depositAmount = 0;

            // 检查多渠道支付信息
            if (deposit.payment_channels && Array.isArray(deposit.payment_channels)) {
              // 多渠道支付：累加所有渠道的金额
              deposit.payment_channels.forEach(paymentChannel => {
                depositAmount += paymentChannel.amount || 0;
              });
            } else {
              // 单一渠道支付：使用原有逻辑
              depositAmount = deposit.deposit_amount || 0;
            }

            if (isNpc) {
              npcRevenueInCents += depositAmount;
            } else {
              nonNpcRevenueInCents += depositAmount;
            }
          }
        });

        console.log('定金NPC统计完成');
      }
    }

    // 转换收入为元（保留精度）
    let npcRevenue = parseFloat((npcRevenueInCents / 100).toFixed(2));
    let nonNpcRevenue = parseFloat((nonNpcRevenueInCents / 100).toFixed(2));
    let totalSessions = npcSessions + nonNpcSessions;
    let totalRevenue = npcRevenue + nonNpcRevenue;

    console.log('NPC统计计算完成:', {
      npc_sessions: npcSessions,
      non_npc_sessions: nonNpcSessions,
      total_sessions: totalSessions
    });

    return {
      npc_sessions: npcSessions,
      non_npc_sessions: nonNpcSessions,
      npc_revenue: npcRevenue.toFixed(2),
      non_npc_revenue: nonNpcRevenue.toFixed(2),
      npc_session_rate: totalSessions > 0 ? ((npcSessions / totalSessions) * 100).toFixed(1) : 0,
      npc_revenue_rate: totalRevenue > 0 ? ((npcRevenue / totalRevenue) * 100).toFixed(1) : 0
    };
  } catch (err) {
    console.error('计算NPC统计失败：', err);
    return {
      npc_sessions: 0,
      non_npc_sessions: 0,
      npc_revenue: 0,
      non_npc_revenue: 0,
      npc_session_rate: 0,
      npc_revenue_rate: 0
    };
  }
}
