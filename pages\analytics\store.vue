<template>
  <view class="page-body">
    <!-- 筛选条件 - 不被遮罩覆盖 -->
    <el-card style="margin-bottom: 20px;">
      <!-- 移动端垂直布局，PC端水平布局 -->
      <el-row :gutter="20" class="filter-row">
        <el-col :xs="24" :sm="24" :md="6" :lg="4" class="filter-col">
          <div class="filter-item">
            <label class="filter-label">时间</label>
            <el-select v-model="queryForm.timeRange" placeholder="请选择时间" clearable :disabled="loading" class="filter-select" @change="onTimeRangeChange">
              <el-option label="今天" value="today"></el-option>
              <el-option label="昨天" value="yesterday"></el-option>
              <el-option label="本周" value="thisWeek"></el-option>
              <el-option label="上周" value="lastWeek"></el-option>
              <el-option label="本月" value="thisMonth"></el-option>
              <el-option label="上月" value="lastMonth"></el-option>
              <el-option label="今年" value="thisYear"></el-option>
              <el-option label="去年" value="lastYear"></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="10" :lg="6" class="filter-col">
          <div class="filter-item">
            <label class="filter-label">时间范围</label>
            <el-date-picker
              v-model="queryForm.customDateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              :disabled="loading"
              class="filter-select"
              @change="onCustomDateRangeChange">
            </el-date-picker>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="6" :lg="4" class="filter-col" v-if="isAdmin">
          <div class="filter-item">
            <label class="filter-label">区域</label>
            <el-select v-model="queryForm.region" placeholder="请选择区域" clearable :disabled="loading" class="filter-select" @change="onRegionChange">
              <el-option label="全部区域" value=""></el-option>
              <el-option
                v-for="region in regionList"
                :key="region.value"
                :label="region.label"
                :value="region.value">
              </el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="6" :lg="4" class="filter-col">
          <div class="filter-item">
            <label class="filter-label">排除主题</label>
            <el-select
              v-model="queryForm.excludedThemes"
              placeholder="选择要排除的主题"
              multiple
              clearable
              :disabled="loading"
              class="filter-select"
              @change="onExcludedThemesChange">
              <el-option
                v-for="theme in themeList"
                :key="theme._id"
                :label="theme.display_text"
                :value="theme._id">
              </el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="6" :lg="3" class="filter-col">
          <div class="filter-buttons">
            <el-button type="primary" size="medium" @click="loadStoreData" :loading="loading" class="filter-btn">查询</el-button>
            <el-button size="medium" @click="resetQuery" :disabled="loading" class="filter-btn">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据范围提示 -->
    <el-alert
      :title="getDataRangeText()"
      type="warning"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;">
    </el-alert>

    <!-- 门店名称列表区域 - 折叠面板 -->
    <el-collapse v-model="activeCollapse" style="margin-bottom: 20px;">
      <el-collapse-item name="storeList">
        <template slot="title">
          <span style="font-weight: 500; color: #333;">全部门店名称</span>
          <span style="margin-left: 10px; color: #999; font-size: 12px;">共 {{ getTotalStoreCount() }} 家门店</span>
        </template>
        <div class="store-names-container">
          <div class="store-names-grid">
            <div
              v-for="(store, index) in filteredStoreList"
              :key="store._id"
              class="store-name-item"
              :class="{ 'disabled': loading }"
              @click="goToStoreDetail(store._id, '门店名称列表')"
            >
              <div class="store-number">{{ index + 1 }}</div>
              <div class="store-info">
                <div class="store-name">{{ store.store_name }}</div>
                <div class="store-address" v-if="store.store_address">{{ getFormattedAddress(store.store_address) }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- 加载提示 - 显示在筛选条件下方 -->
    <SimpleLoading :visible="loading" text="正在获取数据，请稍候..." />

    <!-- 数据内容区域 -->
    <view class="data-content" :style="{ opacity: loading ? 0.5 : 1, pointerEvents: loading ? 'none' : 'auto' }">

    <!-- 基础统计卡片区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ getTotalStoreCount() }}</div>
            <div class="stat-label">总门店数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ storeStats.totalSessions || 0 }}</div>
            <div class="stat-label">场次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ storeStats.totalPlayers || 0 }}</div>
            <div class="stat-label">消费人数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ formatRevenue(storeStats.totalRevenue) }}</div>
            <div class="stat-label">实际收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 平均指标卡片区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card avg-card">
          <div class="stat-item">
            <div class="stat-value">{{ getActiveStoreCount() }}</div>
            <div class="stat-label">活跃门店数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card avg-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ storeStats.avgPerSession ? formatRevenue(storeStats.avgPerSession.avgRevenue) : '0.00' }}</div>
            <div class="stat-label">平均每场金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card avg-card">
          <div class="stat-item">
            <div class="stat-value">{{ storeStats.avgPerSession ? storeStats.avgPerSession.avgPlayers : 0 }}</div>
            <div class="stat-label">平均每场人数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card avg-card">
          <div class="stat-item">
            <div class="stat-value">¥{{ storeStats.avgPerSession ? storeStats.avgPerSession.avgCustomerPrice : 0 }}</div>
            <div class="stat-label">平均每场客单价</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 收款渠道统计区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="24">
        <el-card class="payment-channels-card">
          <div slot="header">
            <span>收款渠道统计</span>
          </div>
          <el-row :gutter="15">
            <el-col :xs="12" :sm="8" :md="6" :lg="3" v-for="(channel, key) in getPaymentChannels()" :key="key">
              <div class="payment-channel-item">
                <div class="channel-name">{{ channel.name }}</div>
                <div class="channel-amount">¥{{ channel.amount }}</div>
                <div class="channel-count">{{ channel.count }}人</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 开场率和NPC统计区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="rate-stats-card">
          <div slot="header">
            <span>开场率统计</span>
          </div>
          <div class="opening-rate-stats">
            <div class="total-rate">
              <div class="rate-value">{{ storeStats.openingRateStats ? storeStats.openingRateStats.total_opening_rate : 0 }}%</div>
              <div class="rate-label">总开场率</div>
            </div>
            <div class="rate-details">
              <div class="detail-item">
                <span>预设场次：{{ storeStats.openingRateStats ? storeStats.openingRateStats.total_preset_sessions : 0 }}</span>
              </div>
              <div class="detail-item">
                <span>实际场次：{{ storeStats.openingRateStats ? storeStats.openingRateStats.total_actual_sessions : 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="rate-stats-card">
          <div slot="header">
            <span>NPC主题占比</span>
          </div>
          <div class="npc-stats">
            <div class="npc-item">
              <div class="npc-title">场次占比</div>
              <div class="npc-value">{{ storeStats.npcStats ? storeStats.npcStats.npc_session_rate : 0 }}%</div>
              <div class="npc-detail">NPC: {{ storeStats.npcStats ? storeStats.npcStats.npc_sessions : 0 }} / 非NPC: {{ storeStats.npcStats ? storeStats.npcStats.non_npc_sessions : 0 }}</div>
            </div>
            <div class="npc-item">
              <div class="npc-title">营业额占比</div>
              <div class="npc-value">{{ storeStats.npcStats ? storeStats.npcStats.npc_revenue_rate : 0 }}%</div>
              <div class="npc-detail">NPC: ¥{{ storeStats.npcStats ? formatRevenue(storeStats.npcStats.npc_revenue) : '0.00' }} / 非NPC: ¥{{ storeStats.npcStats ? formatRevenue(storeStats.npcStats.non_npc_revenue) : '0.00' }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 扇形图区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="chart-card">
          <div slot="header">
            <span>场次分布</span>
          </div>
          <div ref="sessionsChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="chart-card">
          <div slot="header">
            <span>人数分布</span>
          </div>
          <div ref="playersChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="chart-card">
          <div slot="header">
            <span>金额分布</span>
          </div>
          <div ref="revenueChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排名区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="ranking-card">
          <div slot="header">
            <span>门店场次排名</span>
            <span style="float: right; color: #999; font-size: 12px;">合计: {{ getTotalSessions() }}场</span>
          </div>
          <!-- 排名列表 -->
          <div class="ranking-list">
            <div
              v-for="(item, index) in getSessionsRanking()"
              :key="index"
              class="ranking-item"
              :class="{ 'disabled': loading }"
              @click="goToStoreDetail(item.store_id)"
            >
              <div class="ranking-number">
                <span v-if="index < 3" class="crown-icon">👑</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-bar">
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="ranking-value">{{ item.value }}场</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="ranking-card">
          <div slot="header">
            <span>门店人数排名</span>
            <span style="float: right; color: #999; font-size: 12px;">合计: {{ getTotalPlayers() }}人</span>
          </div>
          <!-- 排名列表 -->
          <div class="ranking-list">
            <div
              v-for="(item, index) in getPlayersRanking()"
              :key="index"
              class="ranking-item"
              :class="{ 'disabled': loading }"
              @click="goToStoreDetail(item.store_id)"
            >
              <div class="ranking-number">
                <span v-if="index < 3" class="crown-icon">👑</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-bar">
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="ranking-value">{{ item.value }}人</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="ranking-card">
          <div slot="header">
            <span>门店收入排名</span>
            <span style="float: right; color: #999; font-size: 12px;">合计: {{ getTotalRevenue() }}元</span>
          </div>
          <!-- 排名列表 -->
          <div class="ranking-list">
            <div
              v-for="(item, index) in getRevenueRanking()"
              :key="index"
              class="ranking-item"
              :class="{ 'disabled': loading }"
              @click="goToStoreDetail(item.store_id)"
            >
              <div class="ranking-number">
                <span v-if="index < 3" class="crown-icon">👑</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-bar">
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="ranking-value">¥{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 玩家分布统计区域 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="distribution-card">
          <div slot="header">
            <span>玩家来源分布</span>
            <span style="float: right; color: #999; font-size: 12px;">合计: {{ getTotalSourcePlayers() }}人</span>
          </div>
          <!-- 来源分布列表 -->
          <div class="ranking-list">
            <div
              v-for="(item, index) in getSourceDistribution()"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number">
                <span v-if="index < 3" class="crown-icon">👑</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-bar">
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="ranking-value">{{ item.value }}人</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="distribution-card">
          <div slot="header">
            <span>玩家性别分布</span>
            <span style="float: right; color: #999; font-size: 12px;">合计: {{ getTotalGenderPlayers() }}人</span>
          </div>
          <!-- 性别分布列表 -->
          <div class="ranking-list">
            <div
              v-for="(item, index) in getGenderDistribution()"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number">
                <span v-if="index < 3" class="crown-icon">👑</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-bar">
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="ranking-value">{{ item.value }}人</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="distribution-card">
          <div slot="header">
            <span>玩家类型分布</span>
            <span style="float: right; color: #999; font-size: 12px;">合计: {{ getTotalTypePlayers() }}人</span>
          </div>
          <!-- 类型分布列表 -->
          <div class="ranking-list">
            <div
              v-for="(item, index) in getTypeDistribution()"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number">
                <span v-if="index < 3" class="crown-icon">👑</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-bar">
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="ranking-value">{{ item.value }}人</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>



    </view> <!-- 数据内容区域结束 -->
    <!-- 页面内容结束 -->
  </view>
</template>

<script>
let that; // 当前页面对象
let vk = uni.vk; // vk实例

// 引入ECharts
import * as echarts from 'echarts';
// 引入简单加载组件
import SimpleLoading from '@/components/SimpleLoading/SimpleLoading.vue';

export default {
  components: {
    SimpleLoading
  },
  data() {
    // 页面数据变量
    return {
      // 页面是否请求中或加载中
      loading: false,

      // 折叠面板控制（默认展开，activeCollapse包含面板名称）
      activeCollapse: ['storeList'],

      // 统计数据
      storeStats: {
        totalSessions: 0,
        totalPlayers: 0,
        totalRevenue: 0
      },

      // 门店列表
      storeList: [],

      // 主题列表
      themeList: [],

      // 区域列表
      regionList: [],

      // 查询条件
      queryForm: {
        store_id: "",
        theme_id: "",
        region: "", // 区域筛选
        excludedThemes: [], // 排除的主题列表
        timeRange: "today", // 默认选择今天
        dateRange: [],
        customDateRange: []
      },

      // 图表实例
      sessionsChartInstance: null,
      playersChartInstance: null,
      revenueChartInstance: null,

      // 图表数据
      chartData: {
        sessionsChart: [],
        playersChart: [],
        revenueChart: [],
        sourceDistribution: [],
        genderDistribution: [],
        typeDistribution: []
      }
    };
  },

  // 监听 - 页面每次【加载时】执行(如：前进)
  onLoad() {
    that = this;
    vk = that.vk;
    that.init();
  },

  // 计算属性
  computed: {
    isAdmin() {
      return this.$store.state.$user.userInfo.role.includes('admin');
    },
    // 根据区域筛选后的门店列表
    filteredStoreList() {
      console.log('filteredStoreList 计算中...');
      console.log('storeList:', this.storeList);
      console.log('queryForm.region:', this.queryForm.region);

      if (!this.storeList || this.storeList.length === 0) {
        console.log('storeList 为空，返回空数组');
        return [];
      }

      // 如果没有选择区域，返回所有门店
      if (!this.queryForm.region) {
        console.log('没有选择区域，返回所有门店:', this.storeList.length);
        return this.storeList;
      }

      // 根据区域筛选门店
      let filtered = this.storeList.filter(store => {
        console.log(`门店 ${store.store_name} 的区域:`, store.region, '选择的区域:', this.queryForm.region);
        return store.region === this.queryForm.region;
      });
      console.log('筛选后的门店数量:', filtered.length);
      return filtered;
    }
  },

  // 函数
  methods: {
    // 页面数据初始化函数
    async init() {
      try {
        that.loading = true;
        // 设置默认日期范围为今天
        that.queryForm.dateRange = that.getDateRangeByType('today');

        await that.loadStoreList();
        await that.loadThemeList();
        await that.loadRegionList();
        await that.loadStoreStats();
        await that.loadChartData();
        that.initCharts();
      } catch (error) {
        console.error('页面初始化失败：', error);
        vk.toast('页面加载失败，请重试');
      } finally {
        that.loading = false;
      }
    },

    // 加载门店列表（带权限控制）
    async loadStoreList() {
      try {
        let res = await vk.callFunction({
          url: 'admin/analytics/common/sys/getStoreSelectList',
          data: {}
        });
        if (res.code === 0) {
          that.storeList = res.rows || [];
          console.log('门店列表加载成功:', that.storeList);
          console.log('第一个门店数据结构:', that.storeList[0]);

          // 如果用户只能访问一个门店，自动选择该门店
          if (res.accessLevel === 'single' && that.storeList.length === 1) {
            that.queryForm.store_id = that.storeList[0].value;
          }
        } else {
          vk.toast(res.msg || '加载门店列表失败');
        }
      } catch (err) {
        console.error('加载门店列表失败：', err);
        vk.toast('加载门店列表失败，请重试');
      }
    },

    // 加载主题列表（带权限控制）
    async loadThemeList() {
      try {
        let res = await vk.callFunction({
          url: 'admin/analytics/common/sys/getThemeSelectList',
          data: {
            store_id: that.queryForm.store_id,
            forSelect: false // 获取完整列表格式，包含display_text字段
          }
        });
        if (res.code === 0) {
          that.themeList = res.rows || [];
        } else {
          vk.toast(res.msg || '加载主题列表失败');
        }
      } catch (err) {
        console.error('加载主题列表失败：', err);
        vk.toast('加载主题列表失败，请重试');
      }
    },

    // 加载区域列表
    async loadRegionList() {
      try {
        let res = await vk.callFunction({
          url: 'admin/analytics/common/sys/getRegionList',
          data: {}
        });
        if (res.code === 0) {
          that.regionList = res.data || [];
          console.log('区域列表加载成功:', that.regionList);
        } else {
          vk.toast(res.msg || '加载区域列表失败');
        }
      } catch (err) {
        console.error('加载区域列表失败：', err);
        vk.toast('加载区域列表失败，请重试');
      }
    },

    // 加载统计数据
    async loadStoreStats() {
      try {
        // 判断是否为上月数据查询，如果是则使用简化版本避免超时
        let isLastMonth = that.queryForm.timeRange === 'lastMonth';
        let isLargeTimeRange = false;

        // 检查自定义时间范围是否超过30天
        if (that.queryForm.customDateRange && that.queryForm.customDateRange.length === 2) {
          let timeDiff = that.queryForm.customDateRange[1] - that.queryForm.customDateRange[0];
          let daysDiff = timeDiff / (1000 * 60 * 60 * 24);
          isLargeTimeRange = daysDiff > 30;
        }

        // 选择合适的云函数
        let cloudFunctionUrl = (isLastMonth || isLargeTimeRange) ?
          'admin/analytics/store/sys/getStoreStatsSimple' :
          'admin/analytics/store/sys/getStoreStats';

        console.log('使用云函数:', cloudFunctionUrl, '原因:', isLastMonth ? '上月数据' : isLargeTimeRange ? '大时间范围' : '正常查询');

        let res = await vk.callFunction({
          url: cloudFunctionUrl,
          data: that.queryForm
        });
        if (res.code === 0) {
          that.storeStats = res.data || {};
          console.log('门店统计数据:', that.storeStats);
          console.log('场次数:', that.storeStats.totalSessions);
        } else {
          console.error('获取统计数据失败:', res.msg);
        }
      } catch (err) {
        console.error('加载统计数据失败：', err);
      }
    },
    
    // 加载门店数据
    async loadStoreData() {
      try {
        that.loading = true;
        await that.loadStoreStats();
        await that.loadChartData();
        that.updateCharts();
      } catch (error) {
        console.error('加载门店数据失败：', error);
        vk.toast('加载数据失败，请重试');
      } finally {
        that.loading = false;
      }
    },
    
    // 刷新
    refresh() {
      that.loadStoreData();
    },

    // 重置查询条件
    async resetQuery() {
      try {
        that.loading = true;
        that.queryForm = {
          store_id: "",
          theme_id: "",
          region: "", // 重置区域选择
          excludedThemes: [], // 重置排除主题
          timeRange: "today", // 重置时也默认选择今天
          dateRange: that.getDateRangeByType('today'), // 设置今天的日期范围
          customDateRange: []
        };
        await that.loadThemeList();
        await that.loadStoreData();
      } catch (error) {
        console.error('重置查询失败：', error);
        vk.toast('重置失败，请重试');
      } finally {
        that.loading = false;
      }
    },

    // 时间范围选择变化
    async onTimeRangeChange() {
      if (that.loading) return; // 防止重复触发
      // 清空自定义时间范围
      that.queryForm.customDateRange = [];
      that.queryForm.dateRange = that.getDateRangeByType(that.queryForm.timeRange);
      await that.loadStoreData();
    },

    // 自定义时间范围变化
    async onCustomDateRangeChange() {
      if (that.loading) return; // 防止重复触发
      // 清空预设时间选择
      that.queryForm.timeRange = "";
      if (that.queryForm.customDateRange && that.queryForm.customDateRange.length === 2) {
        // 处理开始时间：设置为当天的 00:00:00
        const startDate = new Date(that.queryForm.customDateRange[0]);
        const startOfDay = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 0, 0, 0, 0);

        // 处理结束时间：设置为当天的 23:59:59
        const endDate = new Date(that.queryForm.customDateRange[1]);
        const endOfDay = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 23, 59, 59, 999);

        that.queryForm.dateRange = [startOfDay.getTime(), endOfDay.getTime()];
      } else {
        that.queryForm.dateRange = [];
      }
      await that.loadStoreData();
    },

    // 根据时间类型获取日期范围
    getDateRangeByType(timeType) {
      if (!timeType) return [];

      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      const date = now.getDate();
      const day = now.getDay(); // 0=周日, 1=周一, ..., 6=周六

      let startDate, endDate;

      switch (timeType) {
        case 'today':
          startDate = that.formatDate(now);
          endDate = that.formatEndDate(now);
          break;

        case 'yesterday':
          const yesterday = new Date(year, month, date - 1);
          startDate = that.formatDate(yesterday);
          endDate = that.formatEndDate(yesterday);
          break;

        case 'thisWeek':
          // 本周一到今天
          const thisWeekStart = new Date(year, month, date - (day === 0 ? 6 : day - 1));
          startDate = that.formatDate(thisWeekStart);
          endDate = that.formatEndDate(now);
          break;

        case 'lastWeek':
          // 上周一到上周日
          const lastWeekStart = new Date(year, month, date - (day === 0 ? 6 : day - 1) - 7);
          const lastWeekEnd = new Date(year, month, date - (day === 0 ? 6 : day - 1) - 1);
          startDate = that.formatDate(lastWeekStart);
          endDate = that.formatEndDate(lastWeekEnd);
          break;

        case 'thisMonth':
          // 本月1号到今天
          const thisMonthStart = new Date(year, month, 1);
          startDate = that.formatDate(thisMonthStart);
          endDate = that.formatEndDate(now);
          break;

        case 'lastMonth':
          // 上月1号到上月最后一天
          const lastMonthStart = new Date(year, month - 1, 1);
          const lastMonthEnd = new Date(year, month, 0);
          startDate = that.formatDate(lastMonthStart);
          endDate = that.formatEndDate(lastMonthEnd);
          break;

        case 'thisYear':
          // 今年1月1号到今天
          const thisYearStart = new Date(year, 0, 1);
          startDate = that.formatDate(thisYearStart);
          endDate = that.formatEndDate(now);
          break;

        case 'lastYear':
          // 去年1月1号到去年12月31号
          const lastYearStart = new Date(year - 1, 0, 1);
          const lastYearEnd = new Date(year - 1, 11, 31);
          startDate = that.formatDate(lastYearStart);
          endDate = that.formatEndDate(lastYearEnd);
          break;

        default:
          return [];
      }

      return [startDate, endDate];
    },

    // 格式化日期为时间戳
    formatDate(date) {
      // 设置为当天的开始时间 00:00:00
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
      return startOfDay.getTime();
    },

    // 格式化日期为当天结束时间戳
    formatEndDate(date) {
      // 设置为当天的结束时间 23:59:59
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);
      return endOfDay.getTime();
    },

    // 门店选择变化时的处理
    onStoreChange() {
      // 清空主题选择
      that.queryForm.theme_id = "";
      // 重新加载主题列表
      that.loadThemeList();
      // 重新加载数据
      that.loadStoreData();
    },

    // 区域选择变化时的处理
    onRegionChange() {
      // 重新加载数据
      that.loadStoreData();
    },

    // 排除主题选择变化时的处理
    async onExcludedThemesChange() {
      if (that.loading) return; // 防止重复触发
      // 重新加载数据
      await that.loadStoreData();
    },

    // 加载图表数据
    async loadChartData() {
      try {
        // 判断是否为上月数据查询，如果是则使用简化版本避免超时
        let isLastMonth = that.queryForm.timeRange === 'lastMonth';
        let isLargeTimeRange = false;

        // 检查自定义时间范围是否超过30天
        if (that.queryForm.customDateRange && that.queryForm.customDateRange.length === 2) {
          let timeDiff = that.queryForm.customDateRange[1] - that.queryForm.customDateRange[0];
          let daysDiff = timeDiff / (1000 * 60 * 60 * 24);
          isLargeTimeRange = daysDiff > 30;
        }

        // 选择合适的云函数
        let cloudFunctionUrl = (isLastMonth || isLargeTimeRange) ?
          'admin/analytics/store/sys/getChartDataSimple' :
          'admin/analytics/store/sys/getChartData';

        console.log('使用图表云函数:', cloudFunctionUrl, '原因:', isLastMonth ? '上月数据' : isLargeTimeRange ? '大时间范围' : '正常查询');

        let res = await vk.callFunction({
          url: cloudFunctionUrl,
          data: that.queryForm
        });
        if (res.code === 0) {
          that.chartData = res.data || {};
          console.log('图表数据加载成功:', that.chartData);
          console.log('sessionsChart 数据:', that.chartData.sessionsChart);
        } else {
          console.error('图表数据加载失败:', res.msg);
        }
      } catch (err) {
        console.error('加载图表数据失败：', err);
      }
    },

    // 初始化图表
    initCharts() {
      that.$nextTick(() => {
        // 初始化场次分布图表
        if (that.$refs.sessionsChart) {
          that.sessionsChartInstance = echarts.init(that.$refs.sessionsChart);
          // 监听窗口大小变化
          window.addEventListener('resize', () => {
            that.sessionsChartInstance && that.sessionsChartInstance.resize();
          });
        }

        // 初始化人数分布图表
        if (that.$refs.playersChart) {
          that.playersChartInstance = echarts.init(that.$refs.playersChart);
          // 监听窗口大小变化
          window.addEventListener('resize', () => {
            that.playersChartInstance && that.playersChartInstance.resize();
          });
        }

        // 初始化金额分布图表
        if (that.$refs.revenueChart) {
          that.revenueChartInstance = echarts.init(that.$refs.revenueChart);
          // 监听窗口大小变化
          window.addEventListener('resize', () => {
            that.revenueChartInstance && that.revenueChartInstance.resize();
          });
        }

        // 更新图表数据
        that.updateCharts();
      });
    },

    // 更新图表数据
    updateCharts() {
      // 更新场次分布图表
      if (that.sessionsChartInstance) {
        let sessionsOption = {
          title: {
            text: '场次分布',
            left: 'center',
            textStyle: {
              fontSize: 14
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              return  params.name + ': ' + params.value + ' (' + params.percent + '%)';
            }
          },
          legend: {
            show: false
          },
          series: [
            {
              name: '场次分布',
              type: 'pie',
              radius: ['30%', '55%'],
              center: ['50%', '60%'],
              avoidLabelOverlap: false,
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c}',
                fontSize: 12,
                color: '#333'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 10
              },
              data: that.chartData.sessionsChart || []
            }
          ]
        };
        that.sessionsChartInstance.setOption(sessionsOption);
      }

      // 更新人数分布图表
      if (that.playersChartInstance) {
        let playersOption = {
          title: {
            text: '人数分布',
            left: 'center',
            textStyle: {
              fontSize: 14
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              return  params.name + ': ' + params.value + ' (' + params.percent + '%)';
            }
          },
          legend: {
            show: false
          },
          series: [
            {
              name: '人数分布',
              type: 'pie',
              radius: ['30%', '55%'],
              center: ['50%', '60%'],
              avoidLabelOverlap: false,
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c}',
                fontSize: 12,
                color: '#333'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 10
              },
              data: that.chartData.playersChart || []
            }
          ]
        };
        that.playersChartInstance.setOption(playersOption);
      }

      // 更新金额分布图表
      if (that.revenueChartInstance) {
        let revenueOption = {
          title: {
            text: '金额分布',
            left: 'center',
            textStyle: {
              fontSize: 14
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              return params.name + ': ¥' + params.value + ' (' + params.percent + '%)';
            }
          },
          legend: {
            show: false
          },
          series: [
            {
              name: '金额分布',
              type: 'pie',
              radius: ['30%', '55%'],
              center: ['50%', '60%'],
              avoidLabelOverlap: false,
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: ¥{c}',
                fontSize: 12,
                color: '#333'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 10
              },
              data: that.chartData.revenueChart || []
            }
          ]
        };
        that.revenueChartInstance.setOption(revenueOption);
      }
    },

    // 获取场次排名数据
    getSessionsRanking() {
      console.log('getSessionsRanking 被调用');
      console.log('chartData.sessionsChart:', that.chartData.sessionsChart);

      if (!that.chartData.sessionsChart) return [];

      // 排序并获取所有数据
      let sortedData = [...that.chartData.sessionsChart]
        .sort((a, b) => b.value - a.value);

      // 计算最大值用于进度条
      let maxValue = sortedData.length > 0 ? sortedData[0].value : 1;

      let result = sortedData.map(item => {
        // 通过门店名称查找对应的门店ID
        let store = that.storeList.find(s => s.store_name === item.name);
        let store_id = store ? store._id : item.store_id;

        console.log(`门店 ${item.name} 的 store_id:`, store_id);

        return {
          store_id: store_id,
          name: item.name,
          value: item.value,
          percentage: Math.round((item.value / maxValue) * 100)
        };
      });

      console.log('getSessionsRanking 返回结果:', result);
      return result;
    },

    // 获取人数排名数据
    getPlayersRanking() {
      if (!that.chartData.playersChart) return [];

      // 排序并获取所有数据
      let sortedData = [...that.chartData.playersChart]
        .sort((a, b) => b.value - a.value);

      // 计算最大值用于进度条
      let maxValue = sortedData.length > 0 ? sortedData[0].value : 1;

      return sortedData.map(item => {
        // 通过门店名称查找对应的门店ID
        let store = that.storeList.find(s => s.store_name === item.name);
        let store_id = store ? store._id : item.store_id;

        return {
          store_id: store_id,
          name: item.name,
          value: item.value,
          percentage: Math.round((item.value / maxValue) * 100)
        };
      });
    },

    // 获取收入排名数据
    getRevenueRanking() {
      if (!that.chartData.revenueChart) return [];

      // 排序并获取所有数据
      let sortedData = [...that.chartData.revenueChart]
        .sort((a, b) => b.value - a.value);

      // 计算最大值用于进度条
      let maxValue = sortedData.length > 0 ? sortedData[0].value : 1;

      return sortedData.map(item => {
        // 通过门店名称查找对应的门店ID
        let store = that.storeList.find(s => s.store_name === item.name);
        let store_id = store ? store._id : item.store_id;

        return {
          store_id: store_id,
          name: item.name,
          value: item.value,
          percentage: Math.round((item.value / maxValue) * 100)
        };
      });
    },

    // 获取场次总计
    getTotalSessions() {
      if (!that.chartData.sessionsChart) return 0;
      return that.chartData.sessionsChart.reduce((sum, item) => sum + item.value, 0);
    },

    // 获取人数总计
    getTotalPlayers() {
      if (!that.chartData.playersChart) return 0;
      return that.chartData.playersChart.reduce((sum, item) => sum + item.value, 0);
    },

    // 格式化收入显示（避免浮点数精度问题）
    formatRevenue(revenue) {
      if (revenue === null || revenue === undefined) return '0.00';
      return parseFloat(revenue).toFixed(2);
    },

    // 获取收入总计
    getTotalRevenue() {
      if (!that.chartData.revenueChart) return 0;
      let total = that.chartData.revenueChart.reduce((sum, item) => sum + item.value, 0);
      return total.toFixed(2);
    },

    // 获取玩家来源分布
    getSourceDistribution() {
      if (!that.chartData.sourceDistribution) return [];

      // 排序并获取所有数据
      let sortedData = [...that.chartData.sourceDistribution]
        .sort((a, b) => b.value - a.value);

      // 计算最大值用于进度条
      let maxValue = sortedData.length > 0 ? sortedData[0].value : 1;

      return sortedData.map(item => ({
        name: item.name,
        value: item.value,
        percentage: Math.round((item.value / maxValue) * 100)
      }));
    },

    // 获取玩家性别分布
    getGenderDistribution() {
      if (!that.chartData.genderDistribution) return [];

      // 排序并获取所有数据
      let sortedData = [...that.chartData.genderDistribution]
        .sort((a, b) => b.value - a.value);

      // 计算最大值用于进度条
      let maxValue = sortedData.length > 0 ? sortedData[0].value : 1;

      return sortedData.map(item => ({
        name: item.name,
        value: item.value,
        percentage: Math.round((item.value / maxValue) * 100)
      }));
    },

    // 获取玩家类型分布
    getTypeDistribution() {
      if (!that.chartData.typeDistribution) return [];

      // 排序并获取所有数据
      let sortedData = [...that.chartData.typeDistribution]
        .sort((a, b) => b.value - a.value);

      // 计算最大值用于进度条
      let maxValue = sortedData.length > 0 ? sortedData[0].value : 1;

      return sortedData.map(item => ({
        name: item.name,
        value: item.value,
        percentage: Math.round((item.value / maxValue) * 100)
      }));
    },

    // 获取来源玩家总数
    getTotalSourcePlayers() {
      if (!that.chartData.sourceDistribution) return 0;
      return that.chartData.sourceDistribution.reduce((sum, item) => sum + item.value, 0);
    },

    // 获取性别玩家总数
    getTotalGenderPlayers() {
      if (!that.chartData.genderDistribution) return 0;
      return that.chartData.genderDistribution.reduce((sum, item) => sum + item.value, 0);
    },

    // 获取类型玩家总数
    getTotalTypePlayers() {
      if (!that.chartData.typeDistribution) return 0;
      return that.chartData.typeDistribution.reduce((sum, item) => sum + item.value, 0);
    },

    // 获取收款渠道数据
    getPaymentChannels() {
      if (!that.storeStats.paymentChannels) return [];

      const channelNames = {
        booking: '预约金额',
        tuangou: '团购',
        kaitianbao: '收款码',
        wechat: '微信',
        cash: '现金',
        douyin: '抖音',
        koubei: '支付宝'
      };

      return Object.keys(channelNames).map(key => ({
        name: channelNames[key],
        amount: (that.storeStats.paymentChannels[key] ? that.storeStats.paymentChannels[key].amount : 0).toFixed(2),
        count: that.storeStats.paymentChannels[key] ? that.storeStats.paymentChannels[key].count : 0
      }));
    },

    // 获取总门店数（根据区域筛选）
    getTotalStoreCount() {
      return this.filteredStoreList ? this.filteredStoreList.length : 0;
    },

    // 获取活跃门店数（有场次数据的门店）
    getActiveStoreCount() {
      if (!that.chartData.sessionsChart) return 0;
      return that.chartData.sessionsChart.filter(item => item.value > 0).length;
    },

    // 格式化地址显示
    getFormattedAddress(addressObj) {
      if (!addressObj) return '';

      try {
        // 如果是字符串，尝试解析为对象
        let address = typeof addressObj === 'string' ? JSON.parse(addressObj) : addressObj;

        let addressParts = [];

        // 按照省、市、区的顺序组合地址
        if (address.province && address.province.name) {
          addressParts.push(address.province.name);
        }
        if (address.city && address.city.name) {
          addressParts.push(address.city.name);
        }
        if (address.area && address.area.name) {
          addressParts.push(address.area.name);
        }

        return addressParts.join(' ');
      } catch (error) {
        console.error('地址格式化失败：', error);
        return '';
      }
    },

    // 跳转到门店详情页面
    goToStoreDetail(store_id, source = '未知来源') {
      console.log(`goToStoreDetail 被调用，来源: ${source}, store_id:`, store_id);
      console.log('当前 loading 状态:', that.loading);

      if (that.loading) {
        vk.toast('数据加载中，请稍候...');
        return;
      }

      if (!store_id) {
        console.error('store_id 为空，无法跳转');
        vk.toast('门店ID不能为空');
        return;
      }

      console.log('准备跳转到门店详情页面，URL:', `/pages/analytics/store-detail?store_id=${store_id}`);

      uni.navigateTo({
        url: `/pages/analytics/store-detail?store_id=${store_id}`,
        success: function(res) {
          console.log('页面跳转成功:', res);
        },
        fail: function(err) {
          console.error('页面跳转失败:', err);
          vk.toast('页面跳转失败，请重试');
        }
      });
    },

    // 获取数据范围提示文本
    getDataRangeText() {
      if (that.queryForm.timeRange) {
        const timeRangeMap = {
          'today': '今天',
          'yesterday': '昨天',
          'thisWeek': '本周',
          'lastWeek': '上周',
          'thisMonth': '本月',
          'lastMonth': '上月',
          'thisYear': '今年',
          'lastYear': '去年'
        };
        return `当前显示 ${timeRangeMap[that.queryForm.timeRange]} 的门店数据统计`;
      } else if (that.queryForm.customDateRange && that.queryForm.customDateRange.length === 2) {
        const startDate = new Date(that.queryForm.customDateRange[0]);
        const endDate = new Date(that.queryForm.customDateRange[1]);
        const formatDate = (date) => {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        };
        return `当前显示 ${formatDate(startDate)} 至 ${formatDate(endDate)} 的门店数据统计`;
      } else {
        return '当前显示全部时间范围的门店数据统计';
      }
    }
  },

  // 页面销毁时清理图表实例
  beforeDestroy() {
    if (that.sessionsChartInstance) {
      that.sessionsChartInstance.dispose();
    }
    if (that.playersChartInstance) {
      that.playersChartInstance.dispose();
    }
    if (that.revenueChartInstance) {
      that.revenueChartInstance.dispose();
    }
  }
};
</script>

<style scoped>
/* 筛选条件响应式样式 */
.filter-row {
  margin-bottom: 15px;
}

.filter-col {
  margin-bottom: 15px;
}

.filter-item {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  margin-right: 10px;
  white-space: nowrap;
  min-width: 60px;
}

.filter-select {
  flex: 1;
}

.filter-buttons {
  display: flex;
  align-items: center;
  min-height: 32px;
  justify-content: flex-end;
}

.filter-btn {
  margin-left: 10px;
}

.filter-btn:first-child {
  margin-left: 0;
}

/* 统一卡片高度 */
.el-card {
  display: flex;
  flex-direction: column;
}

.el-card >>> .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 基础统计卡片 */
.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 15px;
  height: 120px; /* 固定卡片高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-item {
  padding: 20px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 80px; /* 确保内容区域最小高度 */
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
  word-break: break-all;
  line-height: 1.2; /* 控制行高 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.stat-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

/* 收款渠道统计卡片 */
.payment-channels-card {
  min-height: 180px;
}

/* 开场率和NPC统计卡片 */
.rate-stats-card {
  min-height: 220px;
}

/* 图表卡片 */
.chart-card {
  min-height: 420px;
  margin-bottom: 20px;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 350px;
}

/* 排名卡片 */
.ranking-card {
  min-height: 480px;
}

/* 分布统计卡片 */
.distribution-card {
  min-height: 480px;
}

/* 排名列表样式 */
.ranking-list {
  padding: 10px 5px;
  max-height: 400px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 13px;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item:hover {
  background-color: #f9f9f9;
  cursor: pointer;
}

.ranking-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.ranking-item.disabled:hover {
  background-color: transparent;
}

.ranking-number {
  width: 30px;
  text-align: center;
  font-weight: bold;
  font-size: 14px;
  color: #666;
  flex-shrink: 0;
}

.crown-icon {
  color: #FFD700;
  font-size: 14px;
}

.ranking-content {
  flex: 1;
  margin: 0 8px;
  min-width: 0;
}

.ranking-name {
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.ranking-bar {
  width: 100%;
}

.bar-bg {
  width: 100%;
  height: 6px;
  background-color: #e8f4ff;
  border-radius: 3px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #409EFF 0%, #1890ff 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.ranking-value {
  font-size: 12px;
  font-weight: bold;
  color: #333;
  min-width: 50px;
  text-align: right;
  flex-shrink: 0;
}

/* 平均指标卡片样式 */
.avg-card .stat-value {
  color: #67C23A;
}

/* 收款渠道统计样式 */
.payment-channels-card .el-card__body {
  padding: 20px;
  flex: 1;
  display: flex;
  align-items: center;
}

.payment-channel-item {
  text-align: center;
  padding: 15px 5px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.payment-channel-item:hover {
  background: #f0f9ff;
  border-color: #409EFF;
}

.channel-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.channel-amount {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
}

.channel-count {
  font-size: 11px;
  color: #999;
}

/* 开场率统计样式 */
.opening-rate-stats {
  text-align: center;
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.total-rate {
  margin-bottom: 20px;
}

.rate-value {
  font-size: 36px;
  font-weight: bold;
  color: #E6A23C;
  margin-bottom: 8px;
}

.rate-label {
  font-size: 14px;
  color: #666;
}

.rate-details {
  display: flex;
  justify-content: space-around;
}

.detail-item {
  font-size: 13px;
  color: #666;
}

/* NPC统计样式 */
.npc-stats {
  padding: 15px;
  flex: 1;
  display: flex;
  align-items: stretch;
  gap: 15px;
}

.npc-item {
  text-align: center;
  padding: 15px 20px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.npc-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.npc-value {
  font-size: 24px;
  font-weight: bold;
  color: #F56C6C;
  margin-bottom: 8px;
}

.npc-detail {
  font-size: 11px;
  color: #999;
  line-height: 1.4;
}

/* 门店名称列表样式 */
.store-names-container {
  padding: 15px;
}

.store-names-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.store-name-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border: 1px solid #e8f4ff;
  border-radius: 8px;
  background: #fafbfc;
  transition: all 0.3s;
  cursor: pointer;
}

.store-name-item:hover {
  background: #e8f4ff;
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.store-name-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.store-name-item.disabled:hover {
  background: #fafbfc;
  border-color: #e8f4ff;
  transform: none;
  box-shadow: none;
}

.store-number {
  width: 30px;
  height: 30px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.store-info {
  flex: 1;
  min-width: 0;
}

.store-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  line-height: 1.4;
}

.store-address {
  font-size: 11px;
  color: #999;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移动端适配 */
@media (max-width: 768px) {
  /* 筛选条件移动端样式 */
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-label {
    margin-bottom: 8px;
    margin-right: 0;
    min-width: auto;
  }

  .filter-select {
    width: 100%;
  }

  .filter-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .filter-btn {
    flex: 1;
    margin-left: 10px;
  }

  .filter-btn:first-child {
    margin-left: 0;
  }

  /* 统计卡片移动端样式 */
  .stat-card {
    height: 100px; /* 移动端稍微降低卡片高度 */
  }

  .stat-item {
    padding: 15px 0;
    min-height: 60px; /* 移动端调整最小高度 */
  }

  .stat-value {
    font-size: 24px;
    line-height: 1.1; /* 移动端更紧凑的行高 */
  }

  .stat-label {
    font-size: 12px;
  }

  /* 图表移动端样式 */
  .chart-container {
    height: 300px;
  }

  .chart-card {
    min-height: 370px;
  }

  /* 排名卡片移动端样式 */
  .ranking-card {
    min-height: 400px;
    margin-bottom: 20px;
  }

  /* 分布统计卡片移动端样式 */
  .distribution-card {
    min-height: 400px;
    margin-bottom: 20px;
  }

  /* 收款渠道移动端样式 */
  .payment-channels-card {
    min-height: auto;
  }

  .payment-channel-item {
    margin-bottom: 15px;
    height: 80px;
  }

  .channel-amount {
    font-size: 14px;
  }

  /* 开场率和NPC统计移动端样式 */
  .rate-stats-card {
    min-height: 180px;
    margin-bottom: 20px;
  }

  .rate-value {
    font-size: 28px;
  }

  .npc-value {
    font-size: 20px;
  }

  .npc-detail {
    font-size: 10px;
  }

  /* 门店名称列表移动端样式 */
  .store-names-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .store-name-item {
    padding: 10px 12px;
  }

  .store-number {
    width: 25px;
    height: 25px;
    font-size: 11px;
    margin-right: 10px;
  }

  .store-name {
    font-size: 13px;
  }

  .store-address {
    font-size: 10px;
  }
}

/* 小屏幕设备适配 */
@media (max-width: 480px) {
  .stat-card {
    height: 90px; /* 小屏幕进一步降低卡片高度 */
  }

  .stat-item {
    min-height: 50px; /* 小屏幕最小高度 */
  }

  .stat-value {
    font-size: 20px;
    line-height: 1.0; /* 小屏幕最紧凑的行高 */
  }

  .filter-buttons {
    flex-direction: column;
  }

  .filter-btn {
    width: 100%;
    margin-left: 0;
    margin-bottom: 10px;
  }

  .filter-btn:last-child {
    margin-bottom: 0;
  }

  /* 图表小屏幕样式 */
  .chart-container {
    height: 250px;
  }

  .chart-card {
    min-height: 320px;
  }

  /* 排名卡片小屏幕样式 */
  .ranking-card {
    min-height: 350px;
  }

  .ranking-name {
    font-size: 11px;
  }

  .ranking-value {
    font-size: 11px;
    min-width: 40px;
  }

  /* 收款渠道小屏幕样式 */
  .payment-channel-item {
    height: 70px;
  }

  .channel-amount {
    font-size: 12px;
  }

  .channel-name {
    font-size: 11px;
  }

  .channel-count {
    font-size: 10px;
  }

  /* 开场率和NPC统计小屏幕样式 */
  .rate-value {
    font-size: 24px;
  }

  .npc-value {
    font-size: 18px;
  }

  .rate-details {
    flex-direction: column;
    gap: 5px;
  }

  .detail-item {
    font-size: 12px;
  }

  .npc-stats {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
